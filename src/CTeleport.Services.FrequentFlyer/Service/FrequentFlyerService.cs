using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CTeleport.Services.AirlineSettingsClient.Core.Interfaces;
using CTeleport.Services.AirlineSettingsClient.Core.Models;
using CTeleport.Services.FrequentFlyer.Models;
using CTeleport.Services.Search.Shared.Models;
using Serilog;

namespace CTeleport.Services.FrequentFlyer.Service
{
    public class FrequentFlyerService : IFrequentFlyerService
    {
        private readonly IAirlineSettingsClient _airlineSettingsClient;
        private readonly ILogger _logger;

        public FrequentFlyerService(IAirlineSettingsClient airlineSettingsClient, ILogger logger)
        {
            _airlineSettingsClient = airlineSettingsClient;
            _logger = logger;
        }

        public async Task<ICollection<FrequentFlyerProgram>> GetProgramsAsync(IEnumerable<string> codes)
        {
            if (codes == null)
            {
                return Array.Empty<FrequentFlyerProgram>();
            }

            try
            {
                var codesHashset = new HashSet<string>(codes);
                var loyaltyPrograms = await _airlineSettingsClient.GetLoyaltyProgramsAsync();
                return loyaltyPrograms
                    .Where(x => codesHashset.Contains(x.Code))
                    .Select(x => new FrequentFlyerProgram
                    {
                        Carrier = x.Carrier,
                        Code = x.Code,
                        Name = x.Name
                    })
                    .ToArray();
            }
            catch (Exception e)
            {
                _logger.ForContext(nameof(codes), codes).Error(e, "Failed to get loyalty programs by codes");
                return Array.Empty<FrequentFlyerProgram>();
            }
        }

        public async Task<FrequentFlyerInfoForReservation> GetForReservationAsync(FrequentFlyerForReservationRequest request)
        {
            try
            {
                if (request.NumbersByComponentKey?.Any() != true)
                {
                    return FrequentFlyerInfoForReservation.Empty;
                }

                if (request == null) throw new ArgumentNullException(nameof(request), "Request is null");
                if (request.LegSegments == null) throw new ArgumentNullException(nameof(request), "LegSegments is null");
                if (request.PlatingCarrier == null) throw new ArgumentNullException(nameof(request), "PlatingCarrier is null");

                var frequentFlyerInfo = new FrequentFlyerInfoForReservation
                {
                    AllowCorporateCodes = true,
                    FrequentFlyerNumbers = new List<FrequentFlyerNumber>(request.NumbersByComponentKey.Count)
                };

                var loyaltyPrograms = await _airlineSettingsClient.GetLoyaltyProgramsAsync();
                var loyaltyProgramsByCode = loyaltyPrograms.ToDictionary(x => x.Code);

                var odPairs = request.LegSegments.SelectMany(x => x.Select(s => s.SplitOD));
                var componentOrigin = GetComponentOrigin(odPairs);
                var componentDestination = GetComponentDestination(odPairs);
                var componentKey = GetComponentKey(componentOrigin, componentDestination);
                foreach (var frequentFlyerNumberByKey in request.NumbersByComponentKey)
                {
                    if (!string.Equals(frequentFlyerNumberByKey.Key, componentKey, StringComparison.InvariantCultureIgnoreCase))
                    {
                        _logger.Debug("Frequent flyer component keys {Calculated} and {Requested} are different", componentKey, frequentFlyerNumberByKey.Key);
                        continue;
                    }

                    if (!loyaltyProgramsByCode.TryGetValue(frequentFlyerNumberByKey.Value.Code, out var loyaltyProgram))
                    {
                        _logger.ForContext(nameof(frequentFlyerNumberByKey.Value.Code), frequentFlyerNumberByKey.Value.Code)
                            .Warning("Loyalty program not found");
                        continue;
                    }

                    if (!IsProgramApplicable(request.PlatingCarrier, loyaltyProgram))
                    {
                        _logger.ForContext(nameof(frequentFlyerNumberByKey.Value.Code), frequentFlyerNumberByKey.Value.Code)
                            .ForContext(nameof(request.PlatingCarrier), request.PlatingCarrier)
                            .Warning("Loyalty program is not applicable");
                        continue;
                    }

                    frequentFlyerInfo.AllowCorporateCodes = frequentFlyerInfo.AllowCorporateCodes && loyaltyProgram.AllowCorporateNumber;
                    frequentFlyerInfo.FrequentFlyerNumbers.Add(frequentFlyerNumberByKey.Value);
                }

                return frequentFlyerInfo;
            }
            catch (Exception e)
            {
                _logger.ForContext(nameof(request), request, true)
                    .Error(e, "Failed on getting frequent flyer info for reservation");

                return FrequentFlyerInfoForReservation.Empty;
            }
        }

        public async Task<IDictionary<string, FrequentFlyerNumber>> GetFrequentFlyerNumbersAsync(IDictionary<string, Messages.Commands.Models.FrequentFlyerNumber> frequentFlyerNumbers)
        {
            if (frequentFlyerNumbers?.Any() != true)
                return new Dictionary<string, FrequentFlyerNumber>();

            var frequentFlyerPrograms = await GetProgramsAsync(frequentFlyerNumbers.Select(p => p.Value.Code));

            var frequentFlyerNumbersRes = new Dictionary<string, FrequentFlyerNumber>(frequentFlyerNumbers.Count);
            foreach (var (componentKey, frequentFlyerNumber) in frequentFlyerNumbers)
            {
                var program = frequentFlyerPrograms?.FirstOrDefault(c => c.Code == frequentFlyerNumber.Code);
                frequentFlyerNumbersRes.Add(componentKey, new FrequentFlyerNumber
                {
                    Carrier = program?.Carrier,
                    Code = frequentFlyerNumber.Code,
                    Number = frequentFlyerNumber.Number
                });
            }

            return frequentFlyerNumbersRes;
        }

        private ICollection<FrequentFlyerComponent> GetFrequentFlyerComponents(FrequentFlyerRequest request, List<LoyaltyProgram> loyaltyPrograms)
        {
            var allSegments = request.LegSegments.SelectMany(l => l).ToList();
            var frequentFlyerComponentsByKey = new Dictionary<string, FrequentFlyerComponent>();
            var originDestinationPairs = request.LegSegments
                .SelectMany(x => x.Select(s => s.SplitOD))
                .Distinct()
                .ToList();

            foreach (var loyaltyProgram in loyaltyPrograms)
            {
                var applicableODs = originDestinationPairs.Where(odPair =>
                {
                    var split = request.SplitProviderODs?.Find(x => x.OriginDestination == odPair);
                    return split == null || IsProgramApplicable(split.PlatingCarrier, loyaltyProgram);
                }).ToArray();

                if (!applicableODs.Any())
                {
                    continue;
                }

                var returnFlightComponent = IsReturnFlightComponent(allSegments, applicableODs);
                var componentOrigin = GetComponentOrigin(applicableODs);
                var componentDestination = GetComponentDestination(applicableODs);
                var componentKey = GetComponentKey(componentOrigin, componentDestination);
                if (!frequentFlyerComponentsByKey.TryGetValue(componentKey, out var component))
                {
                    component = CreateFrequentFlyerComponent(componentKey, componentOrigin, componentDestination, returnFlightComponent);
                    frequentFlyerComponentsByKey.Add(componentKey, component);
                }

                var rank = CalculateRank(applicableODs, allSegments, loyaltyProgram);
                if (rank == 0)
                {
                    continue;
                }

                component.SupportedFrequentFlyerPrograms.Add(CreateFrequentFlyer(loyaltyProgram, rank));
            }

            NormalizeRanks(frequentFlyerComponentsByKey.Values);

            return frequentFlyerComponentsByKey.Values;
        }

        private static bool IsReturnFlightComponent(ICollection<FlightSegment> allSegments, string[] applicableODs)
        {
            var origin = allSegments.First(x => applicableODs.Contains(x.SplitOD));
            var destination = allSegments.Last(x => applicableODs.Contains(x.SplitOD));
            return origin.Origin == destination.Destination;
        }

        private static FrequentFlyerProgram CreateFrequentFlyer(LoyaltyProgram loyaltyProgram, int rank)
        {
            return new FrequentFlyerProgram
            {
                Code = loyaltyProgram.Code,
                Carrier = loyaltyProgram.Carrier,
                Name = loyaltyProgram.Name,
                Rank = rank
            };
        }

        private static FrequentFlyerComponent CreateFrequentFlyerComponent(string componentKey, string componentOrigin,
            string componentDestination, bool returnFlight = false)
        {
            var nameBuilder = new StringBuilder($"From {componentOrigin} to {componentDestination}");
            if (returnFlight)
            {
                nameBuilder.Append(" and back");
            }

            return new FrequentFlyerComponent
            {
                Key = componentKey,
                Name = nameBuilder.ToString(),
                SupportedFrequentFlyerPrograms = new List<FrequentFlyerProgram>()
            };
        }

        private static string GetComponentKey(string componentOrigin, string componentDestination)
        {
            return $"{componentOrigin}{componentDestination}".ToLowerInvariant();
        }

        private static string GetComponentDestination(IEnumerable<string> applicableODs)
        {
            return applicableODs.Last().Substring(4, 3);
        }

        private static string GetComponentOrigin(IEnumerable<string> applicableODs)
        {
            return applicableODs.First().Substring(0, 3);
        }

        private static int CalculateRank(IEnumerable<string> legs, IReadOnlyCollection<FlightSegment> allSegments, LoyaltyProgram loyaltyProgram)
        {
            var rank = 0;
            foreach (var originDestination in legs)
            {
                rank = allSegments
                    .Where(s => s.SplitOD == originDestination &&
                                IsProgramApplicable(s.Carrier, loyaltyProgram) &&
                                IsProgramApplicable(s.Operator ?? s.Carrier, loyaltyProgram))
                    .Select(x => GetDurationRank(x) + GetProgramOwnerRank(x, loyaltyProgram))
                    .Sum();
            }

            return rank;
        }

        private void NormalizeRanks(ICollection<FrequentFlyerComponent> components)
        {
            var programs = components.SelectMany(c => c.SupportedFrequentFlyerPrograms).ToList();
            if (!programs.Any())
            {
                return;
            }

            var maxRank = programs.Max(p => p.Rank);
            if (maxRank == 0)
            {
                return;
            }

            foreach (var frequentFlyer in programs)
            {
                frequentFlyer.Rank = (int)Math.Floor(frequentFlyer.Rank * 10d / maxRank); // rank is a number from 0 to 10
            }
        }

        private static bool IsProgramApplicable(string carrier, LoyaltyProgram loyaltyProgram)
        {
            return carrier == loyaltyProgram.Carrier ||
                   (loyaltyProgram.ExtraCarriers != null && loyaltyProgram.ExtraCarriers.Contains(carrier));
        }

        private static int GetDurationRank(FlightSegment segment)
        {
            return segment.ArrivalTimestampUtc - segment.DepartureTimestampUtc;
        }

        private static int GetProgramOwnerRank(FlightSegment segment, LoyaltyProgram program)
        {
            return (segment.Operator ?? segment.Carrier) == program.Carrier ? 1 : 0;
        }
    }
}
