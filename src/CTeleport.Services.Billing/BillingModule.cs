using Autofac;
using CTeleport.Common.Extensions;
using CTeleport.Services.Billing.Clients;
using CTeleport.Services.Billing.Configuration;
using CTeleport.Services.Helpers;
using Microsoft.Extensions.Configuration;

namespace CTeleport.Services.Billing
{
    public class BillingModule : Module
    {
        private readonly IConfiguration _configuration;

        public BillingModule(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        protected override void Load(ContainerBuilder builder)
        {
            var options = _configuration.GetSettings<BillingOptions>();
            builder.RegisterInstance(options).SingleInstance();

            builder
                .Register((ctx, p) => ctx.Resolve<IApiClientProxyFactory>().CreateApiClient<IBillingApi>(ctx.Resolve<Configuration.BillingOptions>().Url))
                .As<IBillingApi>();
            builder.RegisterType<BillingClient>().As<IBillingClient>();
        }
    }
}
