using System.Linq;

namespace CTeleport.Services.Billing.Configuration
{
    public class BillingOptions
    {
        public string Url { get; set; }
        
        public bool UseProxy { get; set; }
        public ProxyUrlMap[] Proxy { get; set; }

        public bool ShouldProxy(string path) => UseProxy && Proxy.Any(x => path.StartsWith(x.From));

        public string GetProxyUrl(string path)
        {
            var proxyMap = Proxy.FirstOrDefault(x => path.StartsWith(x.From));
            if (proxyMap == null) return path;

            return path.Replace(proxyMap.From, proxyMap.To);
        }

        public bool GetAuthRequiredOption(string path)
        {
            var proxyMap = Proxy.FirstOrDefault(x => path.StartsWith(x.From));
            var disableAuth = proxyMap?.DisableAuth ?? false;
            return !disableAuth;
        }
    }

    public class ProxyUrlMap
    {
        public string From { get; set; }
        public string To { get; set; }
        public bool DisableAuth { get; set; }
    }
}