using CTeleport.Messages.Commands.Enums;
using CTeleport.Services.Billing.Clients.Dto;
using CTeleport.Services.Excel.Helpers;
using Microsoft.Extensions.Caching.Memory;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Polly;
using RestEase;
using Serilog;
using System;
using System.Diagnostics;
using System.Net;
using System.Threading.Tasks;
using Polly.Retry;

namespace CTeleport.Services.Billing.Clients
{
    [Header("User-Agent", "CTeleport.BillingClient")]
    [Header("accept", "application/json")]
    public interface IBillingApi
    {
        [Get("tenant-preferences/{tenant}")]
        Task<TenantBillingPreferencesDto> GetTenantBillingPreferencesAsync([Path] string tenant);

        [Get("internal/invoicee/{invoiceeId}/details")]
        Task<InvoiceeExtendedDto> GetExtendedInvoiceeAsync([Path] int invoiceeId);
    }

    public class BillingClient : IBillingClient
    {
        private readonly IBillingApi _api;
        private readonly ILogger _log;
        private readonly AsyncRetryPolicy _policy;
        private readonly IMemoryCache _cache;

        public BillingClient(IBillingApi api, ILogger log, IMemoryCache cache)
        {
            _api = api;
            _log = log;
            _cache = cache;

            _policy = Policy
                .Handle<Exception>(e => !(e is ApiException apiException) ||
                                        (apiException.StatusCode != HttpStatusCode.NotFound
                                         && apiException.StatusCode != HttpStatusCode.Forbidden
                                         && apiException.StatusCode != HttpStatusCode.InternalServerError))
                .WaitAndRetryAsync(3, retryAttempt => TimeSpan.FromMilliseconds(300 * retryAttempt),
                    (exception, timeSpan, retryCount, context) =>
                    {
                        _log.Warning(exception, "Exception {ExceptionType} on {RetryCount} retry count", exception.GetType().Name, retryCount);
                    });
        }

        public async Task<TenantBillingPreferencesDto> GetTenantBillingPreferencesAsync(string tenant)
        {
            var key = $"tenant-billing-preferences:{tenant}";
            var prefs = _cache.Get<TenantBillingPreferencesDto>(key);
            if (prefs != null)
            {
                return prefs;
            }

            prefs = await _policy.ExecuteAsync(async () => await _api.GetTenantBillingPreferencesAsync(tenant));
            if (prefs != null)
            {
                _cache.Set(key, prefs);
            }

            return prefs;
        }

        public async Task<InvoiceeExtendedDto> GetExtendedInvoiceeAsync(int invoiceeId)
        {
            return await _policy.ExecuteAsync(async () => await _api.GetExtendedInvoiceeAsync(invoiceeId));
        }
    }

    /// <summary>
    /// Invoice model
    /// </summary>
    public class InvoiceDto
    {
        /// <summary>
        /// Invoice number
        /// </summary>
        public string Number { get; set; }

        /// <summary>
        /// Tenant Id
        /// </summary>
        public string TenantId { get; set; }

        /// <summary>
        /// Invoicee company name
        /// </summary>
        public string Invoicee { get; set; }

        /// <summary>
        /// Invoice amount
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// Invoice currency
        /// </summary>
        public string Currency { get; set; }

        /// <summary>
        /// Invoice creation date, eg '2018-09-04'
        /// </summary>
        [JsonProperty("invoice_date")]
        public string InvoiceDate { get; set; }

        /// <summary>
        /// Invoice due date, eg '2018-09-21'
        /// </summary
        [JsonProperty("due_date")]
        public string DueDate { get; set; }

        /// <summary>
        /// Is invoice received
        /// </summary>
        [JsonProperty("is_received")]
        public bool IsReceived { get; set; }

        /// <summary>
        /// Is invoice due
        /// </summary>
        [JsonProperty("is_due")]
        public bool IsDue { get; set; }

        /// <summary>
        /// Is invoice paid
        /// </summary>
        [JsonProperty("is_paid")]
        public bool IsPaid { get; set; }

        /// <summary>
        /// Amount �ue
        /// </summary>
        [JsonProperty("amount_due")]
        public decimal AmountDue { get; set; }
    }

    /// <summary>
    /// Invoice model
    /// </summary>
    public class InvoiceExportDto
    {
        /// <summary>
        /// Invoice number
        /// </summary>
        [ExportColumn("Invoice number")]
        public string Number { get; set; }

        /// <summary>
        /// Invoicee company name
        /// </summary>
        [ExportColumn("Invoicee")]
        public string Invoicee { get; set; }

        /// <summary>
        /// Invoice amount
        /// </summary>
        [ExportColumn("Amount")]
        public decimal Amount { get; set; }

        /// <summary>
        /// Amount due
        /// </summary>
        [ExportColumn("Amount due")]
        [JsonProperty("amount_due")]
        public decimal AmountDue { get; set; }

        /// <summary>
        /// Invoice currency
        /// </summary>
        [ExportColumn("Currency")]
        public string Currency { get; set; }

        /// <summary>
        /// Invoice creation date, eg '2018-09-04'
        /// </summary>
        [ExportColumn("Issue date")]
        [JsonProperty("invoice_date")]
        public string InvoiceDate { get; set; }

        /// <summary>
        /// Invoice due date, eg '2018-09-21'
        /// </summary
        [ExportColumn("Due date")]
        [JsonProperty("due_date")]
        public string DueDate { get; set; }

        /// <summary>
        /// Is invoice received
        /// </summary>
        [ExportColumn("Is received")]
        [JsonProperty("is_received")]
        public bool IsReceived { get; set; }

        /// <summary>
        /// Is invoice due
        /// </summary>
        [ExportColumn("Is due")]
        [JsonProperty("is_due")]
        public bool IsDue { get; set; }

        /// <summary>
        /// Is invoice paid
        /// </summary>
        [ExportColumn("Is paid")]
        [JsonProperty("is_paid")]
        public bool IsPaid { get; set; }
    }

    /// <summary>
    /// Total due, aggregated by currency
    /// </summary>
    public class DueDto
    {
        /// <summary>
        /// Currency
        /// </summary>
        public string Currency { get; set; }

        /// <summary>
        /// Total due amount
        /// </summary>
        [JsonProperty("total_due")]
        public decimal TotalDue { get; set; }

        /// <summary>
        /// Overdue amount for 3+ days
        /// </summary>
        public decimal Overdue3 { get; set; }

        /// <summary>
        /// Overdue amount for 7+ days
        /// </summary>
        public decimal Overdue7 { get; set; }

        /// <summary>
        /// Overdue amount for 14+ days
        /// </summary>
        public decimal Overdue14 { get; set; }
    }

    /// <summary>
    /// Item in tenant's statement of account
    /// </summary>
    [DebuggerDisplay("{Type} {Amount}")]
    public class StatementItemDto
    {
        /// <summary>
        /// Item type
        /// </summary>
        [ExportColumn("Type")]
        public string Type { get; set; }

        /// <summary>
        /// Invoice number, only for invoice and credit note
        /// </summary>
        [ExportColumn("Invoice number")]
        public string Number { get; set; }

        /// <summary>
        /// Invoicee company name
        /// </summary>
        [ExportColumn("Invoicee")]
        public string Invoicee { get; set; }

        /// <summary>
        /// Vessel name
        /// </summary>
        [ExportColumn("Vessel")]
        public string Vessel { get; set; }

        /// <summary>
        /// Invoice amount
        /// </summary>
        [ExportColumn("Amount")]
        public decimal Amount { get; set; }

        /// <summary>
        /// Amount due
        /// </summary>
        [ExportColumn("Amount due")]
        [JsonProperty("amount_due")]
        public decimal? AmountDue { get; set; }

        /// <summary>
        /// Invoice currency
        /// </summary>
        [ExportColumn("Currency")]
        public string Currency { get; set; }

        /// <summary>
        /// Is received, only for invoice and credit note
        /// </summary>
        [ExportColumn("Is received")]
        [JsonProperty("is_received")]
        public bool? IsReceived { get; set; }

        /// <summary>
        /// Is due, only for invoice
        /// </summary>
        [ExportColumn("Is due")]
        [JsonProperty("is_due")]
        public bool? IsDue { get; set; }

        /// <summary>
        /// Is paid, only for invoice
        /// </summary>
        [ExportColumn("Is paid")]
        [JsonProperty("is_paid")]
        public bool? IsPaid { get; set; }

        /// <summary>
        /// Item creation date, eg '2018-09-04'
        /// </summary>
        [ExportColumn("Date")]
        [JsonProperty("date")]
        public string Date { get; set; }

        /// <summary>
        /// Invoice due date, eg '2018-09-21'
        /// </summary>
        [ExportColumn("Due date")]
        [JsonProperty("due_date")]
        public string DueDate { get; set; }
    }

    /// <summary>
    /// Available payment method type configuration for tenant or invoicee
    /// </summary>
    public class PaymentMethodDto
    {
        /// <summary>
        /// Payment method type configuration id
        /// </summary>
        [JsonProperty("id")]
        public int Id { get; set; }

        /// <summary>
        /// True if this payment method type is default
        /// </summary>
        [JsonProperty("is_default")]
        public bool IsDefault { get; set; }

        /// <summary>
        /// Payment method type
        /// </summary>
        [JsonProperty("payment_method")]
        public PaymentMethodType PaymentMethod { get; set; }
    }

    /// <summary>
    /// Tenant billing preferences
    /// </summary>
    public class TenantBillingPreferencesDto
    {
        /// <summary>
        /// Tenant Id
        /// </summary>
        [JsonProperty("tenant_id")]
        public string TenantId { get; set; }

        /// <summary>
        /// If true, invoice item amount will be rounded to ceiling
        /// </summary>
        [JsonProperty("round_amounts")]
        public bool RoundAmounts { get; set; }
    }

    /// <summary>
    /// Credit card payment model
    /// </summary>
    public class CreditCardPaymentDto
    {
        /// <summary>
        /// Payment id
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// Payment  method fee
        /// </summary>
        public decimal Fee { get; set; }

        /// <summary>
        /// If true, real card was charged
        /// If false, card virtual balance was changed
        /// </summary>
        public bool IsReal { get; set; }

        /// <summary>
        /// Payment state
        /// </summary>
        [JsonConverter(typeof(StringEnumConverter))]
        public CreditCardPaymentState State { get; set; }
    }

    /// <summary>
    /// Credit card payment state
    /// </summary>
    public enum CreditCardPaymentState
    {
        /// <summary>
        /// Payment awaits withdraw
        /// </summary>
        Hold,

        /// <summary>
        /// Amount was captured
        /// </summary>
        Captured,

        /// <summary>
        /// Payment is cancelled
        /// </summary>
        Cancelled,

        /// <summary>
        /// Payment is failed
        /// </summary>
        Failed
    }
}