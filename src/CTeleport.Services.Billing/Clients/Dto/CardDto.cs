namespace CTeleport.Services.Billing.Clients.Dto
{
    /// <summary>
    /// Credit Card object
    /// </summary>
    public class CardDto
    {
        /// <summary>
        /// The ID of the credit card
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// The friendly name of the credit card
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Indicates that the current card is used as default among the other credit cards
        /// </summary>
        public bool Default { get; set; }

        /// <summary>
        /// Mask of the number of the current card which contains last 4 digits of the number.
        /// </summary>
        public string Mask { get; set; }

        /// <summary>
        /// The credit card expiration month
        /// </summary>
        public long ExpMonth { get; set; }

        /// <summary>
        /// The credit card expiration year
        /// </summary>
        public long ExpYear { get; set; }

        /// <summary>
        /// The cardholder name of the current card
        /// </summary>
        public string CardHolderName { get; set; }
    }
}