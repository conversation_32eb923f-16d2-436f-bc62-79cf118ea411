using Autofac;
using CTeleport.Common.Extensions;
using CTeleport.Services.AirGateway.Clients;
using CTeleport.Services.AirGateway.Configuration;
using Microsoft.Extensions.Configuration;

namespace CTeleport.Services.AirGateway;

public class AirGatewayModule : Autofac.Module
{
    private readonly IConfiguration _configuration;

    public AirGatewayModule(IConfiguration configuration)
    {
        _configuration = configuration;
    }

    protected override void Load(ContainerBuilder builder)
    {
        builder.RegisterInstance(_configuration.GetSettings<AirGatewayOptions>()).SingleInstance();
        builder.RegisterType<AirGatewayClient>().As<IAirGatewayClient>();
    }
}
