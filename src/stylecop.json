{"$schema": "https://raw.githubusercontent.com/DotNetAnalyzers/StyleCopAnalyzers/master/StyleCop.Analyzers/StyleCop.Analyzers/Settings/stylecop.schema.json", "settings": {"layoutRules": {"newlineAtEndOfFile": "require"}, "indentation": {"indentationSize": 4}, "documentationRules": {"documentInterfaces": true, "documentInternalElements": false, "xmlHeader": false}, "namingRules": {}, "orderingRules": {"usingDirectivesPlacement": "outsideNamespace", "elementOrder": ["kind", "constant", "accessibility", "static", "readonly"]}}}