using System;
using System.Collections.Generic;
using CTeleport.Services.VoidCalc.Configuration;
using CTeleport.Services.VoidCalc.Interfaces;
using NodaTime;

namespace CTeleport.Services.VoidCalc.Services.Carriers;

/// <summary>
/// Same day midnight void time
/// </summary>
public sealed class SameDayMidnightVoidTimeResolver : IVoidTimeResolver
{
    private static readonly HashSet<string> PlatingCarriers = ["N4", "H4"];
    private readonly IVoidTimeCalculator _voidTimeCalculator;

    public SameDayMidnightVoidTimeResolver(IVoidTimeCalculator voidTimeCalculator)
    {
        _voidTimeCalculator = voidTimeCalculator;
    }

    public bool IsApplicable(string platingCarrier, IReadOnlySet<string> marketingCarriers, string source)
    {
        return PlatingCarriers.Contains(platingCarrier);
    }

    public DateTime Calculate(VoidRule voidRule, DateTimeZone pccTz, DateTime issueAt, DateTime departureAt)
    {
        return _voidTimeCalculator.CalculateLastVoidTime(VoidRule.SameDayMidnight, pccTz, issueAt, departureAt);
    }
}