using NodaTime;

namespace CTeleport.Services.VoidCalc.Interfaces
{
    /// <summary>
    /// SourceTimeZoneResolverService is responsible to retrieve and cache the timezone of sources
    /// </summary>
    public interface ISourceTimeZoneResolverService
    {
        /// <summary>
        /// Issues the ticket for specified reservation
        /// </summary>
        /// <param name="source">The id of a source, e.g. "1G.8WG0"</param>
        /// <returns>TimeZone for requested source</returns>        
        DateTimeZone GetSourceTimeZone(string source);
    }
}