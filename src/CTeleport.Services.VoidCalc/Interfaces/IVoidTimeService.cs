using System;
using System.Collections.Generic;

namespace CTeleport.Services.VoidCalc.Interfaces
{
    /// <summary>
    /// Helper methods for calculation void times.
    /// </summary>
    public interface IVoidTimeService
    {
        /// <summary>
        /// Calculates Last void time based on raw parameters.
        /// </summary>
        /// <param name="source">Source Id.async Eg: 1G.8WG0</param>
        /// <param name="carrier">Plating carrier.</param>
        /// <param name="departureAt">Flight departure date in UTC.</param>
        /// <param name="marketingCarriers">Set of marketing carriers</param>
        /// <param name="issueAt">Ticked issue date in UTC. Null if the ticket wasnt issued yet.</param>
        /// <returns>Last moment for voiding a ticket in the circunstances provided as parameters.</returns>
        DateTime CalculateLastVoidTime(string source, string carrier, DateTime departureAt, IReadOnlySet<string> marketingCarriers, DateTime? issueAt = null);
    }
}