using System.Reflection;
using Autofac;
using CTeleport.Common.Messaging.Services;
using CTeleport.Services.Cancellation.Communication.Http.Core;
using CTeleport.Services.Cancellation.Operations;

namespace CTeleport.Services.Cancellation;

public class CancellationModule : Autofac.Module
{
    protected override void Load(ContainerBuilder builder)
    {
        RegisterInternalCancellationHandlers(builder);
    }

    private static void RegisterInternalCancellationHandlers(ContainerBuilder builder)
    {
        builder.RegisterType<AutofacCancellationHandlerResolver>()
            .As<ICancellationHandlerResolver>()
            .InstancePerLifetimeScope();
        
        //
        var assembly = typeof(CancellationModule).Assembly;
        var openHandlerType = typeof(ICancellationServiceRequestHandler<,>);

        var handlerTypes = assembly
            .GetTypes()
            .Where(t => t is { IsClass: true, IsAbstract: false })
            .Select(t => new
            {
                Type = t,
                HandlerInterfaces = t.GetInterfaces()
                    .Where(i => i.IsGenericType && i.GetGenericTypeDefinition() == openHandlerType)
                    .ToList(),
            })
            .Where(x => x.HandlerInterfaces.Any());

        foreach (var h in handlerTypes)
        {
            foreach (var t in h.HandlerInterfaces)
            {
                var genericArgs = t.GetGenericArguments();
                var requestType = genericArgs[0];
                var responseType = genericArgs[1];
                var key = HandlerKeyGenerator.Create(requestType, responseType);
                
                builder.RegisterType(h.Type)
                    .Keyed(key, t)
                    .InstancePerLifetimeScope();
            }
        }
    }
}