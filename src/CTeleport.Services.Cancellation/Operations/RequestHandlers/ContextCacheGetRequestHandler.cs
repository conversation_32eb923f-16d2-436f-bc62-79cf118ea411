using AutoMapper;
using CTeleport.Services.Booking.Cache;
using CTeleport.Services.Cancellation.Communication.Contracts.Messages;
using CTeleport.Services.Cancellation.Communication.Http.Core;
using CTeleport.Services.Cancellation.Extensions;
using CTeleport.Services.Cancellation.Models.Dto;
using Serilog;

namespace CTeleport.Services.Cancellation.Operations.RequestHandlers;

public class ContextCacheGetRequestHandler(
    ICommandContextCache contextCache,
    IMapper mapper,
    ILogger logger) : ICancellationServiceRequestHandler<ContextCache.Get.Request, ContextCache.Get.Response>
{
    public Task<ContextCache.Get.Response> HandleAsync(ContextCache.Get.Request request)
    {
        var ctx = logger
            .ForContext("Request", request, true);
        
        try
        {
            var data = contextCache.Get(request.Key);
            
            var result = Result<CommandContext?>.Ok(data.MapTo<CommandContext>(mapper));
            var response = ContextCache.Get.Response.FromResult(result);
            
            return Task.FromResult(response);
        }
        catch (Exception ex)
        {
            ctx.Error(ex, $"Error while processing {nameof(ContextCache.Get)}");
            
            var result = Result<CommandContext?>.Fail(ex.Message);
            var response = ContextCache.Get.Response.FromResult(result);
            
            return Task.FromResult(response);
        }
    }
}