using CTeleport.Services.Booking.Cache;
using CTeleport.Services.Cancellation.Communication.Contracts.Messages;
using CTeleport.Services.Cancellation.Communication.Http.Core;
using CTeleport.Services.Cancellation.Models.Dto;
using Serilog;

namespace CTeleport.Services.Cancellation.Operations.RequestHandlers;

public class ContextCacheSetRequestHandler(ICommandContextCache contextCache, ILogger logger) : ICancellationServiceRequestHandler<ContextCache.Set.Request, ContextCache.Set.Response>
{
    public Task<ContextCache.Set.Response> HandleAsync(ContextCache.Set.Request request)
    {
        var ctx = logger
            .ForContext("Request", request, true);
        
        try
        {
            contextCache.Set(request.Command);
            
            var response = ContextCache.Set.Response.FromResult(Result.Ok());
            return Task.FromResult(response);
        }
        catch (Exception ex)
        {
            ctx.Error(ex, $"Error while processing {nameof(ContextCache.Set.Request)}");
            
            var response = ContextCache.Set.Response.FromResult(Result.Fail(ex.Message)); 
            return Task.FromResult(response);
        }
    }
}