using CTeleport.Services.Booking.Services;
using CTeleport.Services.Cancellation.Communication.Contracts.Messages;
using CTeleport.Services.Cancellation.Communication.Http.Core;
using CTeleport.Services.Cancellation.Models.Dto;
using CTeleport.Services.Cancellation.Models.Dto.Booking;
using Serilog;

namespace CTeleport.Services.Cancellation.Operations.RequestHandlers;

public class GetSagaRequestHandler(
    IBookingSagaService bookingSagaService,
    ILogger logger) : ICancellationServiceRequestHandler<GetSaga.Request, GetSaga.Response>
{
    public async Task<GetSaga.Response> HandleAsync(GetSaga.Request request)
    {
        var ctx = logger
            .ForContext("BookingId", request.BookingId);
        
        try
        {
            var saga = await bookingSagaService.GetAsync(request.BookingId);
            if (saga == null)
            {
                ctx.Information("No booking saga found for booking ID {BookingId}", request.BookingId);
                return GetSaga.Response.FromResult(Result<BookingSaga?>.Ok(null));
            }
            
            var sagaAlreadyCancelled = saga?.Reservations?
                .Single(x => x.Id == request.ReservationId)
                .State == Booking.Domain.Aggregates.BookingAggregate.Enums.ReservationState.Cancelled;
            
            var result = new BookingSaga
            {
                Id = saga?.Id,
                BookingId = request.BookingId,
                IsAlreadyCancelled = sagaAlreadyCancelled,
            };
            
            return GetSaga.Response.FromResult(Result<BookingSaga?>.Ok(result));
        }
        catch (Exception ex)
        {
            ctx.Error(ex, $"Error while processing {nameof(GetSaga)}");
            return GetSaga.Response.FromResult(Result<BookingSaga?>.Fail(ex.Message));
        }
    }
}