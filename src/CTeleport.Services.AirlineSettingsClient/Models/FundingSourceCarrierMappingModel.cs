using System.Collections.Generic;
using System.Text.Json.Serialization;
using Newtonsoft.Json;

namespace CTeleport.Services.AirlineSettingsClient.Models;

public class FundingSourceCarrierMappingModel
{
    private const string FundingSourcePropertyName = "funding_source";
    private const string ContentSourcePropertyName = "content_source";
    private const string PlatingCarriersPropertyName = "plating_carriers";

    [JsonProperty(FundingSourcePropertyName)]
    [JsonPropertyName(FundingSourcePropertyName)]
    public string FundingSource { get; set; }

    [JsonProperty(ContentSourcePropertyName)]
    [JsonPropertyName(ContentSourcePropertyName)]
    public string ContentSource { get; set; }

    [JsonProperty(PlatingCarriersPropertyName)]
    [JsonPropertyName(PlatingCarriersPropertyName)]
    public IReadOnlyCollection<string> PlatingCarriers { get; set; }
}