using System;
using System.Runtime.Serialization;

namespace CTeleport.Services.AirlineSettingsClient.Core.Exeptions
{
    [Serializable]
    public class AirlineSettingsNotFoundException : Exception
    {
        public AirlineSettingsNotFoundException()
        {
        }

        public AirlineSettingsNotFoundException(string message) : base(message)
        {
        }

        public AirlineSettingsNotFoundException(string message, Exception innerException) : base(message, innerException)
        {
        }

        protected AirlineSettingsNotFoundException(SerializationInfo info, StreamingContext context) : base(info, context)
        {
        }
    }
}
