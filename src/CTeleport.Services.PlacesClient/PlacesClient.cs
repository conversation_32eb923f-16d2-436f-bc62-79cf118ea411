using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using Microsoft.Extensions.Caching.Memory;
using RestEase;
using Polly;
using Serilog;
using CTeleport.Services.Helpers;
using CTeleport.Services.Search.Shared.Models;
using Polly.Retry;

namespace CTeleport.Services.PlacesApiClient
{
    [Header("User-Agent", "CTeleport.PlacesClient")]
    public interface IPlacesApi
    {
        [Get("/airports/{iata}")]
        Task<Response<Airport>> GetAirportAsync([Path] string iata);

        [Get("/cities/{iata}/airports")]
        Task<Response<IEnumerable<Airport>>> GetCityAirportsAsync([Path] string iata);
    }

    public class PlacesClient : IPlacesClient
    {
        private readonly IPlacesApi _api;
        private readonly PlacesApiOptions _options;
        private readonly IMemoryCache _cache;

        private readonly ILogger _logger;
        private readonly AsyncRetryPolicy _policy;

        private static TimeSpan DEFAULT_TTL = TimeSpan.FromMinutes(60);

        public PlacesClient(PlacesApiOptions options, ILogger logger, IMemoryCache memoryCache)
        {
            _api = RestClient.For<IPlacesApi>(options.Url, HttpRequestHelper.AddApmTracingHeader);
            _options = options;
            _cache = memoryCache;
            _logger = logger;
            _policy = Policy
                .Handle<Exception>(e => !(e is ApiException apiException) ||
                (apiException.StatusCode != HttpStatusCode.NotFound
                 && apiException.StatusCode != HttpStatusCode.Forbidden
                 && apiException.StatusCode != HttpStatusCode.InternalServerError))
                .WaitAndRetryAsync(3, retryAttempt => TimeSpan.FromMilliseconds(300 * retryAttempt),
                    (exception, timeSpan, retryCount, context) =>
                    {
                        _logger.Warning(exception, "Exception {ExceptionType} on {RetryCount} retry count", exception.GetType().Name, retryCount);
                    });
        }

        public async Task<Airport> GetAirportByCodeAsync(string code)
        {
            var airport = await GetUsingCacheAndPolicy(() => _api.GetAirportAsync(code), $"places/airports:{code}");
            if (airport == null)
            {
                _logger.Error("Could not retrieve details for airport {Code}", code);
            }

            return airport;
        }

        private async Task<T> GetUsingCacheAndPolicy<T>(Func<Task<Response<T>>> getCall, string cacheKey)
        {
            if (_cache.TryGetValue(cacheKey, out T result)) return result;

            var policyResult = await _policy.ExecuteAndCaptureAsync(async () =>
            {
                using (var response = await getCall())
                {
                    // prevent uncaught exceptions (e.g. 404)
                    if (!response.ResponseMessage.IsSuccessStatusCode)
                        return default;

                    var content = response.GetContent();
                    _cache.Set(cacheKey, content, response.ResponseMessage.Headers.CacheControl.MaxAge ?? DEFAULT_TTL);
                    return content;
                }
            });

            return policyResult.Result;
        }

        public async Task<IReadOnlyCollection<Airport>> GetAirportsByCodeAsync(string[] codes)
        {
            var airports = await Task.WhenAll(codes.Select(GetAirportByCodeAsync));
            return airports.Where(a => a != null).ToList();
        }

        public async Task<IEnumerable<Airport>> GetCityAirportsAsync(string code)
        {
            var airports = await GetUsingCacheAndPolicy(() => _api.GetCityAirportsAsync(code), $"places/city-airports:{code}");
            if (airports == null)
            {
                _logger.Error("Could not retrieve city {Code} airports", code);
            }

            return airports;
        }
    }
}