using System.Collections.Generic;
using CTeleport.Services.Ancillary.Shared;

namespace CTeleport.Services.ExtraServiceManagement.Dtos
{
    public class AncillariesDto
    {
        public List<ExtraBaggageGroupDto> Baggage { get; set; } 
    }
    
    public class ExtraBaggageGroupDto
    {
        /// <summary>
        /// Key of group. Concat of segment refs 
        /// </summary>
        public string GroupKey { get; set; }
        
        /// <summary>
        /// Segments that are supported for extra service  
        /// </summary>
        public List<SegmentRefDto> Segments  { get; set; }
        public List<BaggageComponentDto> Options { get; set; }
       
    }

    public class BaggageComponentDto
    {
        public BaggageType Type { get; set; }
        public List<BaggageOptionDto> Details { get; set; }

    }

    public class BaggageOptionDto
    {
        /// <summary>
        /// Key to buy extra baggage option from provider
        /// </summary>
        public string Key { get; set; }
        public int? Weight { get; set; }
        public decimal Price { get; set; }
        
        /// <summary>
        /// Currency
        /// </summary>
        public string Ccy { get; set; }
        public string Description { get; set; }
        public string Dimensions { get; set; }
    }

    public class SegmentRefDto
    {
        /// <summary>
        /// iata code of origin  
        /// </summary>
        public string OriginIata { get; set; }
        
        /// <summary>
        /// iata code of destination  
        /// </summary>
        public string DestinationIata { get; set; }
    }
}