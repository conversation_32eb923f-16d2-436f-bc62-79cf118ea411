using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CTeleport.Common.Exceptions;
using CTeleport.Messages.Commands.Bookings;
using CTeleport.Services.Booking.Core;
using CTeleport.Services.ExtraServiceManagement.Clients;
using CTeleport.Services.ExtraServiceManagement.Models;
using CTeleport.Services.ExtraServiceManagement.Shared.Contracts;
using Serilog;
using Serilog.Events;

namespace CTeleport.Services.ExtraServiceManagement.Services
{
    public class AncillaryService : IAncillaryService
    {
        private readonly ILogger _logger;
        private readonly IExtraServiceManagementClient _extraServiceManagementClient;
        public AncillaryService(ILogger logger, 
            IExtraServiceManagementClient extraServiceManagementClient)
        {
            _logger = logger;
            _extraServiceManagementClient = extraServiceManagementClient;
        }
        
        public async Task<FlightSolutionAncillaries> GetOfferedAncillariesAsync(string flightSolutionId)
        {
            var flightSolutionAncillaries =  await _extraServiceManagementClient.GetFlightSolutionAncillariesAsync(flightSolutionId);

            return new FlightSolutionAncillaries(flightSolutionId)
            {
                ExtraBaggageOptions = flightSolutionAncillaries.ExtraBaggageOptions
            };
        }

        public void ValidateSelectedAncillaries(FlightSolutionAncillaries flightSolutionAncillaries, CreateBooking createBooking)
        {
            if(flightSolutionAncillaries == null)
                return;
            
            if (createBooking?.Ancillaries == null || !createBooking.Ancillaries.Any())
            {
                return;
            }

            var selectedAncillaryKeys = createBooking.Ancillaries.Select(x => x.Key).ToList();
            var isSelectedAncillariesExistInOfferedAncillaries = selectedAncillaryKeys.All(x => flightSolutionAncillaries.ExtraBaggageOptions?.Any(y => string.Equals(y.Key, x)) == true);

            if (!isSelectedAncillariesExistInOfferedAncillaries)
            {
                _logger.ForContext(LogEventLevel.Error, nameof(CreateBooking), createBooking, destructureObjects: true).Error("Provider ancillaries not found");
                throw new ValidationException(OperationCodes.NotFound, "Provider ancillaries not found");
            }
        }

        public IReadOnlyCollection<AncillaryModel> GetSelectedAncillaries(string providerKey,IReadOnlyCollection<Messages.Commands.Models.Ancillary> selectedAncillaries,
            FlightSolutionAncillaries flightSolutionAncillaries)
        {
            if (selectedAncillaries == null || flightSolutionAncillaries?.ExtraBaggageOptions == null)
                return Array.Empty<AncillaryModel>();

            var selectedAncillaryKeys = selectedAncillaries.Select(x => x.Key);
            var providerAncillaries = flightSolutionAncillaries.ExtraBaggageOptions
                .Where(x => string.Equals(providerKey, x.SolutionProviderKey) && selectedAncillaryKeys.Contains(x.Key))
                .ToList();

            return providerAncillaries.Select(ToSagaAncillary).ToList();
        }

        private static AncillaryModel ToSagaAncillary(FlightSolutionExtraBaggageDto extraBaggageOption)
            => new()
            {
                Ccy = extraBaggageOption.NetPrice.Currency.IsoCode,
                Description = extraBaggageOption.Description,
                Key = extraBaggageOption.Key,
                Price = extraBaggageOption.NetPrice.Amount,
                Markup = extraBaggageOption.Markup.Amount,
                TotalPrice = extraBaggageOption.TotalPrice.Amount,
                UserCurrency = extraBaggageOption.TotalPrice.Currency.IsoCode,
                Type = extraBaggageOption.Type,
                Weight = extraBaggageOption.Weight,
                SolutionProviderKey = extraBaggageOption.SolutionProviderKey,
                SegmentRefs = extraBaggageOption.SegmentRefs
            };
    }
}