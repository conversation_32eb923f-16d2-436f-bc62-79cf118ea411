using System;
using System.Collections.Generic;
using System.Net;
using System.Threading.Tasks;
using CTeleport.Common.Exceptions;
using CTeleport.Services.Booking.Core.Dto;
using CTeleport.Services.Changes.Core.Dto;
using CTeleport.Services.Helpers;
using CTeleport.Services.Search.Shared.Models;
using CTeleport.Services.Travelport.Configuration;
using Newtonsoft.Json;
using Polly;
using Polly.CircuitBreaker;
using Polly.Wrap;
using RestEase;
using Serilog;

namespace CTeleport.Services.Travelport.Clients
{
    public interface ITravelportApi
    {
        [Post("air/reservation/retrive")]
        [Header("Accept", "application/json")]
        [Header("User-Agent", "CTeleport.Api")]
        Task<ProviderRetrieveReservationResponse> GetReservationDetailsAsync([Body] ProviderRetrieveReservationRequest request);

        [Post("air/reservation/confirm")]
        [Header("Accept", "application/json")]
        [Header("User-Agent", "CTeleport.Api")]
        Task<ConfirmReservationResponse> ConfirmReservationAsync([Body] ConfirmReservationRequest request);

        [Post("air/reservation/reprice")]
        [Header("Accept", "application/json")]
        [Header("User-Agent", "CTeleport.Api")]
        Task<ProviderRepriceReservationResponse> GetRepricingForFareRefreshAsync([Body] ProviderRepriceReservationRequest request);

        [Post("air/reservation/refreshfare")]
        [Header("Accept", "application/json")]
        [Header("User-Agent", "CTeleport.Api")]
        Task<ProviderFareRefreshResponse> RefreshFareMaskAsync([Body] ProviderFareRefreshRequest request);

        [Post("air/availability/solutions")]
        [Header("Accept", "application/json")]
        [Header("User-Agent", "CTeleport.Api")]
        Task<ProviderAvailabilityResponse> GetAvailabilitySolutionsAsync([Body] ProviderAvailabilityRequest request);

        [Post("air/ticket/retrive")]
        [Header("Accept", "application/json")]
        [Header("User-Agent", "CTeleport.Api")]
        Task<ProviderRetrieveTicketResponse> GetTicketStateAsync([Body] ProviderRetrieveTicketRequest request);

        [Post("air/classdrop/reprice")]
        [Header("Accept", "application/json")]
        [Header("User-Agent", "CTeleport.Api")]
        Task<ProviderRepriceReservationResponse> GetRepricingForClassdropAsync([Body]ProviderRepriceReservationRequest request);

        [Post("air/changes-quotation/rebook")]
        [Header("Accept", "application/json")]
        [Header("User-Agent", "CTeleport.Api")]
        Task<ProviderInternalQuotationResponse> GetQuotationsAsync([Body] ProviderChangesQuotationRequest request);

        [Post("air/changes-quotation/modify-fare")]
        [Header("Accept", "application/json")]
        [Header("User-Agent", "CTeleport.Api")]
        Task<ProviderFareModifierResponse> ModifyFareAsync([Body] ProviderFareModifierRequest request);

        [Post("air/changes-quotation/fast-quotation")]
        [Header("Accept", "application/json")]
        [Header("User-Agent", "CTeleport.Api")]
        Task<IList<FlightSolution>> GetFastQuotationAsync([Body] ProviderChangesQuotationRequest request);

        [Post("air/changes-quotation/pnr-aware-quotation")]
        [Header("Accept", "application/json")]
        [Header("User-Agent", "CTeleport.Api")]
        Task<FlightSolution> GetPnrAwareQuotationAsync([Body] ProviderChangesQuotationRequest request);
    }

    public class TravelportClient : ITravelportClient
    {
        private readonly ITravelportApi _api;
        private readonly AsyncPolicyWrap _policy;
        private readonly ILogger _logger;

        public TravelportClient(TravelportOptions options, ILogger logger)
        {
            _logger  = logger;
            _api = new RestClient(options.Url, HttpRequestHelper.AddApmTracingHeader)
            {
                JsonSerializerSettings = new JsonSerializerSettings()
                {
                    ContractResolver = new TravelportJsonSerializer()
                }
            }.For<ITravelportApi>();

            var waitAndRetryPolicy = Policy
                .Handle<Exception>(e => !(e is BrokenCircuitException))
                .WaitAndRetryAsync(2, retryAttempt => TimeSpan.FromMilliseconds(200 * retryAttempt),
                    (exception, timeSpan, retryCount, context) =>
                    {
                        if (exception is ApiException)
                        {
                            if ((exception as ApiException).StatusCode == HttpStatusCode.NotFound)
                            {
                                _logger.Debug(exception, "NotFound Exception {ExceptionType} on {RetryCount} retry count", exception.GetType().Name, retryCount);
                                throw new NotFoundException("Requested entity was not Found");
                            }

                            if ((exception as ApiException).StatusCode == HttpStatusCode.ServiceUnavailable && retryCount > 1)
                            {
                                throw new ExternalServiceException("Couldn't access Travelport api");
                            }
                        }
                        _logger.Warning(exception, "Exception {ExceptionType} on {RetryCount} retry count", exception.GetType().Name, retryCount);
                    });

            var circuitBreakerPolicy = Policy
                .Handle<Exception>()
                .CircuitBreakerAsync(
                    exceptionsAllowedBeforeBreaking: 2,
                    durationOfBreak: TimeSpan.FromMinutes(5),
                    onBreak: (ex, breakDelay) =>
                    {
                        _logger.Warning(ex, "Breaking the circuit due to exception {ExceptionType} for {BreakDelay} minutes", ex.GetType().Name, breakDelay.TotalMinutes);
                    },
                    onReset: () =>
                    {
                        _logger.Debug("Circuit breaker closed");
                    },
                    onHalfOpen: () =>
                    {
                        _logger.Debug("Circuit breaker half-open");
                    }
                );

            _policy = circuitBreakerPolicy.WrapAsync(waitAndRetryPolicy);
        }

        public async Task<ProviderRetrieveReservationResponse> GetReservationAsync(ProviderRetrieveReservationRequest request)
        {
            return await _policy.ExecuteAsync(async () => await _api.GetReservationDetailsAsync(request));
        }

        public async Task<ConfirmReservationResponse> ConfirmReservationAsync(ConfirmReservationRequest request)
        {
            return await _policy.ExecuteAsync(async () => await _api.ConfirmReservationAsync(request));
        }

        public async Task<ProviderAvailabilityResponse> GetAvailabilitySolutionsAsync(ProviderAvailabilityRequest request)
        {
            return await _policy.ExecuteAsync(async () => await _api.GetAvailabilitySolutionsAsync(request));
        }

        public async Task<ProviderRepriceReservationResponse> GetRepricingForFareRefreshAsync(ProviderRepriceReservationRequest request)
        {
            return await _policy.ExecuteAsync(async () => await _api.GetRepricingForFareRefreshAsync(request));
        }

        public async Task<ProviderFareRefreshResponse> RefreshFareMaskAsync(ProviderFareRefreshRequest request)
        {
            return await _policy.ExecuteAsync(async () => await _api.RefreshFareMaskAsync(request));
        }

        public async Task<ProviderRetrieveTicketResponse> GetTicketStateAsync(ProviderRetrieveTicketRequest request)
        {
            return await _policy.ExecuteAsync(async () => await _api.GetTicketStateAsync(request));
        }

        public async Task<ProviderRepriceReservationResponse> GetRepricingForClassdropAsync(ProviderRepriceReservationRequest request)
        {
            return await _policy.ExecuteAsync(async () => await _api.GetRepricingForClassdropAsync(request));
        }

        public async Task<ProviderInternalQuotationResponse> GetRebookQuotationsAsync(ProviderChangesQuotationRequest request)
        {
            return await _policy.ExecuteAsync(async () => await _api.GetQuotationsAsync(request));
        }

        public async Task<ProviderFareModifierResponse> ModifyFareAsync(ProviderFareModifierRequest request)
        {
            return await _policy.ExecuteAsync(async () => await _api.ModifyFareAsync(request));
        }

        public async Task<FlightSolution> GetPnrAwareQuotationAsync(ProviderChangesQuotationRequest request)
        {
            return await _policy.ExecuteAsync(async () => await _api.GetPnrAwareQuotationAsync(request));
        }

        public async Task<IList<FlightSolution>> GetFastQuotationAsync(ProviderChangesQuotationRequest request)
        {
            return await _policy.ExecuteAsync(async () => await _api.GetFastQuotationAsync(request));
        }
    }
} 