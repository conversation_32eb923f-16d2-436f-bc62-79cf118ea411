using System.ComponentModel.DataAnnotations;

namespace CTeleport.Services.Airlines.Models
{
    public class Airline
    {
        /// <summary>
        /// Airline IATA code
        /// </summary>
        [Required]
        [RegularExpression("^[a-zA-Z0-9]{2}[a-zA-Z]?$")]
        public string IATA { get; set; }
        
        /// <summary>
        /// Approved C Teleport name
        /// </summary>
        public string Name { get; set; }
    }
}