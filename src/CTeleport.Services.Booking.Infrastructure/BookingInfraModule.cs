using Autofac;
using CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate;
using CTeleport.Services.Booking.Infrastructure.Repositories;

namespace CTeleport.Services.Booking.Infrastructure;

public class BookingInfraModule : Module
{
    protected override void Load(ContainerBuilder builder)
    {
        builder.RegisterType<BookingRepository>().As<IBookingRepository>();
        builder.RegisterType<DatabaseInitializer>().As<IDatabaseInitializer>();
    }
}