using CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.DomainEvents;
using MongoDB.Driver;

namespace CTeleport.Services.Booking.Infrastructure.Repositories;

public static class Queries
{
    private const string BookingsAggregateEvents = "bookings-aggregate-events";

    public static IMongoCollection<BookingAggregateEvent> BookingAggregateEvents(this IMongoDatabase database)
        => database.GetCollection<BookingAggregateEvent>(BookingsAggregateEvents).WithReadPreference(ReadPreference.PrimaryPreferred);
}