using CTeleport.Services.Search.Shared.Enums;

namespace CTeleport.Services.ChangeExecution.Models;

public class ChangeRequestDetail
{
       /// <summary>
        /// ChangeRequest Id
        /// </summary>
        public string ChangeRequestId { get; set; }

        /// <summary>
        /// Booking Id
        /// </summary>
        public string BookingId { get; set; }

        /// <summary>
        /// Flight Solution Id
        /// </summary>
        public string FlightSolutionId { get; set; }

        /// <summary>
        /// Request id for new booking, for cases when ChangeType is Rebook
        /// </summary>
        public string NewBookingRequestId { get; set; }

        /// <summary>
        /// Request id for change execution, for cases when ChangeType is ReissueInstant
        /// </summary>
        public string ExecutionRequestId { get; set; }

        /// <summary>
        /// New booking id, for cases when ChangeType is Rebook
        /// </summary>
        public string NewBookingId { get; set; }

        /// <summary>
        /// Change type
        /// </summary>
        public ChangeType ChangeType { get; set; }

        /// <summary>
        /// Spend limit set by user, when null, no limit applies
        /// </summary>
        public decimal? SpendLimit { get; set; }

        /// <summary>
        /// Change request state
        /// </summary>
        public string State { get; set; }
        
        /// <summary>
        /// Quoted price diff between original ticket and new ticket, Quotation currency 
        /// </summary>
        public decimal? PriceDiff { get; set; }

        /// <summary>
        /// Change fee, Quotation currency
        /// </summary>
        public decimal? ChangeFee { get; set; }

        /// <summary>
        /// Change fee, Source currency
        /// </summary>
        public decimal? ChangeFeeInSourceCurrency { get; set; }
        
        /// <summary>
        /// Quotation currency
        /// </summary>
        public string Currency { get; set; }
        
        /// <summary>
        /// Source currency
        /// </summary>
        public string OriginalCurrency { get; set; }

        /// <summary>
        /// New date and time of the first changed segment, UTC
        /// </summary>
        public DateTime DepartureAt { get; set; }

        /// <summary>
        /// Date and time when change request was sumitted, UTC
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// Date and time when change request was updated, UTC
        /// </summary>
        public DateTime UpdatedAt { get; set; }

        /// <summary>
        /// Date and time when change request was completed, UTC
        /// </summary>
        public DateTime? CompletedAt { get; set; }

        /// <summary>
        /// Date and time when change request was cancelled, UTC
        /// </summary>
        public DateTime? CancelledAt { get; set; }

        /// <summary>
        /// Flight solution net price on quotation in source currency
        /// </summary>
        public decimal? QuotedPriceNet { get; set; }

        /// <summary>
        /// Provider key
        /// </summary>
        public string ProviderKey { get; set; }
        
        /// <summary>
        /// Funding source for new ticket.
        /// </summary>
        public FundingSourceInfo? FundingSource { get; set; }
        
        public sealed class FundingSourceInfo
        {
            /// <summary>Type of funding source</summary>
            public string Type { get; set; }

            /// <summary>Identifier of specified funding source type</summary>
            public string Id { get; set; }
        }
}