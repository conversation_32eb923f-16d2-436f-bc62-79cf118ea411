using AutoMapper;
using CTeleport.Services.Booking.Enums;
using CTeleport.Services.Booking.Models;

namespace CTeleport.Services.ClassDrop;

public class AutoMapperConfig : Profile
{
    public AutoMapperConfig()
    {
        CreateMap<Booking.Models.Booking, ClassDrop.Models.Dto.Booking.BookingDto>();
        CreateMap<Reservation, ClassDrop.Models.Dto.Reservation.ReservationDto>();
        CreateMap<CheckClassDropResult, ClassDrop.Models.Dto.CheckClassDropResult>();
        CreateMap<PassengerDetails, ClassDrop.Models.Dto.Booking.Passenger>();
        CreateMap<Ticket, ClassDrop.Models.Dto.Reservation.Ticket>();
        CreateMap<TicketPrice, ClassDrop.Models.Dto.Reservation.TicketPrice>();
        CreateMap<FareChange, ClassDrop.Models.Dto.FareChange>();
        CreateMap<Providers.Dto.AirProvider, Models.Dto.AirProvider>();
        CreateMap<FareChangeReason, Models.Dto.Enum.FareChangeReason>();
    }
}