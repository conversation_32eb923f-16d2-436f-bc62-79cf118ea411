using CTeleport.Services.ClassDrop.Communication.Contracts.Messages;
using CTeleport.Services.ClassDrop.Communication.Http.Core;
using CTeleport.Services.ClassDrop.Models.Dto;
using CTeleport.Services.Settings.Services;
using Serilog;

namespace CTeleport.Services.ClassDrop.Operations.RequestHandlers;

public class CheckSourceGloballyEnabledRequestHandler(
    ISettingsService settingsService,
    ILogger logger) : IClassDropServiceRequestHandler<CheckSourceGloballyEnabled.Request, CheckSourceGloballyEnabled.Response>
{
    public async Task<CheckSourceGloballyEnabled.Response> HandleAsync(CheckSourceGloballyEnabled.Request request)
    {
        var ctx = logger
            .ForContext("Source", request.Source);

        logger.Information("Checking if source {Source} is globally enabled", request.Source);
        
        try
        {
            // validate the input
            if (string.IsNullOrEmpty(request.Source))
                return CheckSourceGloballyEnabled.Response.FromResult(Result<bool>.Ok(false));

            // make a request to the settings service 
            var isGloballyEnabled = await settingsService.CheckSourceGloballyEnabled(request.Source);
            
            // build a result 
            return CheckSourceGloballyEnabled.Response.FromResult(Result<bool>.Ok(isGloballyEnabled));
        }
        catch (Exception ex)
        {
            ctx.Error(ex, $"Error while processing {nameof(CheckSourceGloballyEnabled)}");
            
            var result = Result<bool>.Fail(ex.Message);
            return CheckSourceGloballyEnabled.Response.FromResult(result);
        }
    }
}