using CTeleport.Services.Booking.Models;
using CTeleport.Services.Helpers;
using Serilog.Core;
using Serilog.Events;

namespace CTeleport.Services.ClassDrop
{
    public class ReservationEnricher : ILogEventEnricher
    {
        private readonly Reservation _reservation;

        public ReservationEnricher(Reservation reservation)
        {
            _reservation = reservation;
        }

        public void Enrich(LogEvent logEvent, ILogEventPropertyFactory propertyFactory)
        {
            logEvent.AddPropertyIfAbsent(propertyFactory.CreateProperty("BookingId", _reservation.BookingId));
            logEvent.AddPropertyIfAbsent(propertyFactory.CreateProperty("ReservationId", _reservation.Id));
            logEvent.AddPropertyIfAbsent(propertyFactory.CreateProperty("Provider", _reservation.Source));
            logEvent.AddPropertyIfAbsent(propertyFactory.CreateProperty("ReservationState", _reservation.State));
            logEvent.AddPropertyIfAbsent(propertyFactory.CreateProperty("Locator", LocatorsHelper.GetProviderCode(_reservation.Locators)));
        }
    }
}