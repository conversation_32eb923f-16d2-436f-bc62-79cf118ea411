using System;
using System.Threading.Tasks;
using CTeleport.Services.Travelport.Clients;
using CTeleport.Services.Booking.Core;
using CTeleport.Services.Booking.Core.Dto;
using CTeleport.Services.Booking.Core.Interfaces;
using Serilog;

namespace CTeleport.Services.Booking.Travelport.Services
{
    public class ConfirmReservationService : IProviderConfirmReservationService
    {
        private readonly ITravelportClient _travelportClient;
        private readonly ILogger _logger;

        public ConfirmReservationService(ITravelportClient travelportClient, ILogger logger)
        {
            _travelportClient = travelportClient;
            _logger = logger;
        }

        public async Task<ConfirmReservationResponse> ConfirmReservationAsync(ConfirmReservationRequest request)
        {
            try
            {
                _logger.Debug("[TravelportReservationService] Confirming Reservation: {@locator}",
                    request.Locators);
                return await _travelportClient.ConfirmReservationAsync(request);
            }
            catch (Exception e)
            {
                _logger.ForContext(nameof(request.CorrelationId), request.CorrelationId).Error(e, "Error confirming reservation at Travelport in ConfirmReservationAsync() for locator: {@locator}.", request.Locators);
                return new ConfirmReservationResponse().WithError(ErrorCodes.Error, "Error while confirming reservation at provider.");
            }
        }
    }
}