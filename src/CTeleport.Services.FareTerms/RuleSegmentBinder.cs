using System.Collections.Generic;
using System.Linq;
using CTeleport.Services.FareTerms.Shared.Dto;

namespace CTeleport.Services.FareTerms
{
    public abstract class RuleSegmentBinder<TRuleContainer, TSegment, TComponent>
        where TComponent : RuleComponentItem, new()
    {
        private Dictionary<string, string> _componentKeyConversion;

        public readonly List<TComponent> RuleComponents = new();

        public List<Segment> AddTicket(IDictionary<string, TRuleContainer> fareRules, IEnumerable<IEnumerable<TSegment>> legs)
        {
            _componentKeyConversion = new Dictionary<string, string>(fareRules.Count);
            foreach (var (fareComponentKey, container) in fareRules)
            {
                var text = ExtractText(container);
                if (text == null)
                {
                    _componentKeyConversion[fareComponentKey] = null;
                    continue;
                }

                var currentRuleKey = RuleComponents.Count.ToString();
                var item = new TComponent
                {
                    Key = currentRuleKey,
                    Text = text
                };

                AddExtraInfo(item);
                RuleComponents.Add(item);
                _componentKeyConversion[fareComponentKey] = currentRuleKey;
            }

            var result = new List<Segment>();
            var legIndex = 0;
            foreach (var leg in legs)
            {
                foreach (var sourceSegment in leg)
                {
                    var segment = ConvertSegment(sourceSegment);
                    segment.LegIndex = legIndex;
                    result.Add(segment);
                }

                legIndex++;
            }

            if (result.Any(s => s.PenaltyRuleComponentKey == null))
            {
                FixPenaltyRuleComponentKey(result);
            }

            return result;
        }

        protected void FixPenaltyRuleComponentKey(List<Segment> segments)
        {
            foreach (var segment in segments)
            {
                segment.PenaltyRuleComponentKey = null;
            }

            var endIndex = 0;
            foreach (var (od, penaltyRuleComponentKey) in _componentKeyConversion)
            {
                var array = od.Split('-');
                var origin = array[0];
                var destination = array[1];

                var startIndex = segments.FindIndex(endIndex, s => s.Origin == origin);
                if (startIndex < 0)
                    break;

                endIndex = 1 + segments.FindIndex(startIndex, s => s.Destination == destination);
                if (endIndex <= 0)
                    break;

                for (int i = startIndex; i < endIndex; i++)
                {
                    segments[i].PenaltyRuleComponentKey = penaltyRuleComponentKey;
                }
            }
        }

        protected string ConvertComponentKey(string fareComponentKey)
            => fareComponentKey != null && _componentKeyConversion.TryGetValue(fareComponentKey, out string penaltyRuleKey)
                ? penaltyRuleKey
                : null;

        protected virtual void AddExtraInfo(TComponent item) { }

        protected abstract string ExtractText(TRuleContainer container);

        protected abstract Segment ConvertSegment(TSegment source);
    }
}