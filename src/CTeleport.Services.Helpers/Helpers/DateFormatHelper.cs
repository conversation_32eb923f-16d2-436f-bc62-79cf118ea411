using System;
using System.Globalization;

namespace CTeleport.Services.Helpers
{
    public static class DateFormatHelper
    {
        private const string DefaultDateFormat = "yyyy-MM-dd";
        private const string MonthDayYearDateFormat = "MM/dd/yyyy HH:mm:ss";
        
        public static readonly string[] DateFormats = [MonthDayYearDateFormat, DefaultDateFormat];

        public static DateTime ToDateTime(string datetime, string dateFormat) =>
            DateTime.ParseExact(datetime, dateFormat, CultureInfo.InvariantCulture);
        
        public static DateTime ToDateTime(string datetime, string[] dateFormats) =>
            DateTime.ParseExact(datetime, dateFormats, CultureInfo.InvariantCulture);

        public static string AddDays(string date, int days, string dateFormat = DefaultDateFormat)
        {
            var dt = ToDateTime(date, dateFormat)
                .AddDays(days);
            return dt.ToString(dateFormat);
        }
    }
}