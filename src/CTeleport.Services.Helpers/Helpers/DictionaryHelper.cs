using System;
using System.Collections.Generic;
using System.Linq;
using CTeleport.Common.Extensions;

namespace CTeleport.Services.Helpers
{
    public static class DictionaryHelper
    {
        public static Dictionary<TKey, TValue> Union<TKey, TValue>(Dictionary<TKey, TValue> d1, Dictionary<TKey, TValue> d2)
        {
            if (d1 == null || d1.Count == 0)
                return d2;

            if (d2 == null || d2.Count == 0)
                return d1;

            return d1.Concat(d2).ToDictionary(i => i.Key, i => i.Value);
        }

        public static Dictionary<TKey, TValue> UnionWithOverride<TKey, TValue>(Dictionary<TKey, TValue> d1, Dictionary<TKey, TValue> d2)
        {
            if (d1 == null || d1.Count == 0)
                return d2;

            if (d2 == null || d2.Count == 0)
                return d1;

            var result = new Dictionary<TKey, TValue>(d1.Count + d2.Count);
            foreach (var (key, value) in d1.Concat(d2))
            {
                result[key] = value;
            }
            return result;
        }
    }
}
