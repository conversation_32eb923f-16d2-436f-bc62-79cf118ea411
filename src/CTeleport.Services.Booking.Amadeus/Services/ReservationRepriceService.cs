using System;
using System.Threading.Tasks;
using CTeleport.Services.Amadeus.Clients;
using CTeleport.Services.Booking.Core;
using CTeleport.Services.Booking.Core.Dto;
using CTeleport.Services.Booking.Core.Interfaces;
using Serilog;

namespace CTeleport.Services.Booking.Amadeus.Services
{
    public class ReservationRepriceService : IProviderRepriceReservationService
    {
        private readonly IAmadeusClient _amadeusClient;
        private readonly ILogger _logger;

        public ReservationRepriceService(IAmadeusClient amadeusClient, ILogger logger)
        {
            _amadeusClient = amadeusClient;
            _logger = logger;
        }

        public async Task<ProviderRepriceReservationResponse> RepriceReservationAsync(ProviderRepriceReservationRequest request)
        {
            try
            {
                _logger.ForContext(nameof(request.CorrelationId), request.CorrelationId).Debug("[AmadeusReservationRepriceService] Requesting FareRefresh Reprice details for locator: {@locator}",
                    request.Locators);
                return await _amadeusClient.GetRepricingForFareRefreshAsync(request);
            }
            catch (Exception e)
            {
                _logger.ForContext(nameof(request.CorrelationId), request.CorrelationId).Error(e, "Error getting FareRefresh Reprice for reservation {@locator} in Amadeus", request.Locators);
                return new ProviderRepriceReservationResponse().WithError(ErrorCodes.Error, "Error while repricing a reservation in provider (FareRefresh)");
            }
        }

        public async Task<ProviderFareRefreshResponse> RefreshFareMaskAsync(ProviderFareRefreshRequest request)
        {
            try
            {
                _logger.ForContext(nameof(request.CorrelationId), request.CorrelationId).Debug("[AmadeusReservationRepriceService] Requesting RefreshFareMaskAsync for locator: {@locator}",
                    request.Locators);
                return await _amadeusClient.RefreshFareMaskAsync(request);
            }
            catch (Exception e)
            {
                _logger.ForContext(nameof(request.CorrelationId), request.CorrelationId).Error(e, "Error refreshing Fare in Amadeus for reservation {@locator} in Amadeus", request.Locators);
                return new ProviderFareRefreshResponse().WithError(ErrorCodes.Error, "error while requesting RefreshFareMask in provider");
            }
        }
    }
}
