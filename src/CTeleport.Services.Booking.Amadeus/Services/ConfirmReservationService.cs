using System;
using System.Threading.Tasks;
using CTeleport.Services.Amadeus.Clients;
using CTeleport.Services.Booking.Core;
using CTeleport.Services.Booking.Core.Dto;
using CTeleport.Services.Booking.Core.Interfaces;
using Serilog;

namespace CTeleport.Services.Booking.Amadeus.Services
{
    public class ConfirmReservationService : IProviderConfirmReservationService
    {
        private readonly IAmadeusClient _amadeusClient;
        private readonly ILogger _logger;

        public ConfirmReservationService(IAmadeusClient amadeusClient, ILogger logger)
        {
            _amadeusClient = amadeusClient;
            _logger = logger;
        }

        public async Task<ConfirmReservationResponse> ConfirmReservationAsync(ConfirmReservationRequest request)
        {
            try
            {
                return new ConfirmReservationResponse().WithError(ErrorCodes.Error, "Not implemented in Amadeus.");
                // return new ConfirmReservationResponse()
                // {
                //     FlightReservation = new Shared.Models.ProviderFlightReservation()
                // };
            }
            catch (Exception e)
            {
                _logger.ForContext(nameof(request.CorrelationId), request.CorrelationId).Error(e, "Error confirming reservation at Amadeus in ConfirmReservationAsync() for locator: {@locator}.", request.Locators);
                return new ConfirmReservationResponse().WithError(ErrorCodes.Error, "Error while confirming reservation at provider.");
            }
        }
    }
}