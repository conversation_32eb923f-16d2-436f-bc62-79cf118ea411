using System.Threading.Tasks;
using CTeleport.Services.Billing.Clients;
using CTeleport.Services.Price.Client;
using CTeleport.Services.Price.Models;
using CTeleport.Services.Price.Shared.Models;
using CTeleport.Services.Settings.Clients;

namespace CTeleport.Services.Price.Services
{
    public class PriceService : IPriceService
    {
        private readonly IPriceClient _priceClient;
        private readonly IBillingClient _billingClient;
        private readonly ITenantManagementServiceClient _tenantManagementService;

        public PriceService(IPriceClient priceClient, IBillingClient billingClient, ITenantManagementServiceClient tenantManagementService)
        {
            _priceClient = priceClient;
            _billingClient = billingClient;
            _tenantManagementService = tenantManagementService;
        }

        public async Task<PriceConverter> CreatePriceConverterAsync(string tenantId, ReservationPriceDto reservationPrice)
        {
            var tenantSettings = await _tenantManagementService.GetTenantAsync(tenantId);
            var billingPreferences = await _billingClient.GetTenantBillingPreferencesAsync(tenantId);

            var priceConverterSettings = new PriceConverterSettings
            {
                Kickback = tenantSettings.Kickback,
                Markup = reservationPrice.Markup,
                CompensationMarkup = reservationPrice.CompensationMarkup,
                ConsolidatorMarkup = reservationPrice.ConsolidatorMarkup,
                ConvRates = reservationPrice.ConversionRates,
                MarkupCcy = reservationPrice.MarkupCurrency,
                TargetCcy = reservationPrice.Currency,
                ConvMarginsRates = reservationPrice.ConversionMarginRates,
                HighFareMarkup = tenantSettings.HighFareMarkup,
                MarineFareMarkup = tenantSettings.MarineFareMarkup,
                RoundTotalPrice = billingPreferences.RoundAmounts
            };
            
            return PriceConverter.FromPriceConverterSettings(priceConverterSettings);
        }

        public async Task<PriceConverter> CreatePriceConverterAsync(string tenantId, string vessel, string originalCcy, string targetCcy, string sourceId)
        {
            var settings = await _priceClient.GetPriceConverterSettings(tenantId, vessel, originalCcy, targetCcy, sourceId);

            return PriceConverter.FromPriceConverterSettings(settings);
        }
    }
}
