using Autofac;
using Autofac.Extensions.DependencyInjection;
using CTeleport.Common.Apm.Extensions;
using CTeleport.Common.Authorization.Clients;
using CTeleport.Common.Authorization.Extensions;
using CTeleport.Common.Extensions;
using CTeleport.Common.Helpers;
using CTeleport.Common.Logging.Extensions;
using CTeleport.Infrastructure;
using CTeleport.Infrastructure.Http;
using CTeleport.Infrastructure.Serilog;
using CTeleport.Services.AirlineSettingsClient;
using CTeleport.Services.Amadeus;
using CTeleport.Services.Billing;
using CTeleport.Services.Booking.Api.ChangeHandlers;
using CTeleport.Services.Booking.Api.ChangeHandlers.Interfaces;
using CTeleport.Services.Booking.Api.Clients;
using CTeleport.Services.Booking.Api.Clients.Factories;
using CTeleport.Services.Booking.Api.Configuration;
using CTeleport.Services.Booking.Api.Extensions;
using CTeleport.Services.Booking.Api.HostedServices;
using CTeleport.Services.Booking.Api.Infrastructure;
using CTeleport.Services.Booking.Api.Validators;
using CTeleport.Services.Booking.Infrastructure;
using CTeleport.Services.Booking.Models;
using CTeleport.Services.Booking.Repositories.Queries;
using CTeleport.Services.Booking.Repositories.TransactionManagement;
using CTeleport.Services.Booking.Services.SafeOperations;
using CTeleport.Services.ExtraServiceManagement;
using CTeleport.Services.FareCache;
using CTeleport.Services.FareTerms;
using CTeleport.Services.FlightStatus.Amadeus;
using CTeleport.Services.FlightStatus.Travelfusion;
using CTeleport.Services.FlightStatus.Travelport;
using CTeleport.Services.FrequentFlyer;
using CTeleport.Services.Galileo.Configuration;
using CTeleport.Services.Helpers;
using CTeleport.Services.PlacesApiClient;
using CTeleport.Services.Price;
using CTeleport.Services.SearchProxy;
using CTeleport.Services.Settings;
using CTeleport.Services.Travelfusion;
using CTeleport.Services.ApprovalQueueClient;
using CTeleport.Services.ApprovalQueueClient.Configuration;
using CTeleport.Services.Travelport;
using CTeleport.Services.VoidCalc;
using Elastic.Apm.DiagnosticSource;
using Elastic.Apm.MongoDb;
using FluentValidation;
using FluentValidation.AspNetCore;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Mvc.Versioning;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using MongoDB.Driver;
using ProxyKit;
using Serilog;
using System;
using System.Diagnostics.CodeAnalysis;
using System.IO;
using System.Threading.Tasks;
using CTeleport.Common.Messaging.CAP.HealthCheck;
using CTeleport.Common.Messaging.CAP.MongoDB;
using CTeleport.Common.Messaging.HealthCheck;
using CTeleport.FeatureFlag.Resolver.FeatBit.Utils;
using CTeleport.HealthChecks.Core;
using CTeleport.Services.AirGateway;
using CTeleport.Services.Booking.AirGateway;
using CTeleport.Services.Booking.Orchestrator.Client;
using CTeleport.Services.Cancellation;
using CTeleport.Services.Cancellation.Communication.Http;
using CTeleport.Services.ChangeExecution;
using CTeleport.Services.Co2Emissions;
using CTeleport.Services.FlightStatus.AirGateway;
using CTeleport.Services.TravelportJson;
using CTeleport.Services.UsersClient;
using Microsoft.Extensions.Configuration;
using Microsoft.FeatureManagement;
using CTeleport.Services.Booking.Configuration;
using CTeleport.Services.ClassDrop;

namespace CTeleport.Services.Booking.Api
{
    [SuppressMessage("AsyncUsage.CSharp.Naming", "UseAsyncSuffix:Use Async suffix")]
    public class Program
    {
        public static async Task<int> Main(string[] args)
        {
            try
            {
                var builder = WebApplication.CreateBuilder(new WebApplicationOptions
                {
                    ApplicationName = typeof(Program).Assembly.FullName,
                    ContentRootPath = Directory.GetCurrentDirectory()
                });

                var config = builder.Configuration;

                builder.Host
                    .ConfigureHost(config)
                    .UseServiceProviderFactory(new AutofacServiceProviderFactory())
                    .UseLogging(c => c.Enrich.WithTraceContext())
                    .ConfigureContainer<ContainerBuilder>(containerBuilder =>
                    {
                        containerBuilder.RegisterModule(new FareTermsModule(config));
                        containerBuilder.RegisterModule(new FareCacheModule(config));
                        containerBuilder.RegisterModule(new VoidCalcModule(config));
                        containerBuilder.RegisterModule(new SearchProxyModule(config));
                        containerBuilder.RegisterModule(new FrequentFlyerModule(config));
                        containerBuilder.RegisterModule(new SettingsModule(config));
                        containerBuilder.RegisterModule(new CustomFields.CustomFieldsModule(config));
                        containerBuilder.RegisterModule(new BookingModule(config));
                        containerBuilder.RegisterModule(new TravelportModule(config));
                        containerBuilder.RegisterModule(new TravelportJsonModule(config));
                        containerBuilder.RegisterModule(new Travelport.TravelportBookingModule(config));
                        containerBuilder.RegisterModule(new Travelfusion.TravelfusionBookingModule(config));
                        containerBuilder.RegisterModule(new Amadeus.AmadeusBookingModule(config));
                        containerBuilder.RegisterModule(new AmadeusModule(config));
                        containerBuilder.RegisterModule(new AirGatewayBookingModule(config));
                        containerBuilder.RegisterModule(new AirGatewayModule(config));
                        containerBuilder.RegisterModule(new TravelfusionModule(config));
                        containerBuilder.RegisterModule(new PlacesClientModule(config));
                        containerBuilder.RegisterModule(new AirlineSettingsClientModule(config));
                        containerBuilder.RegisterModule(new ApprovalQueueClientModule(config));
                        containerBuilder.RegisterModule(new UsersClientModule(config));
                        containerBuilder.RegisterModule(new BillingModule(config));
                        containerBuilder.RegisterModule(new Airlines.AirlinesClientModule(config));
                        containerBuilder.RegisterModule(new CheckFare.Amadeus.AmadeusCheckFareModule(config));
                        containerBuilder.RegisterModule(new CheckFare.Travelport.TravelportCheckFareModule(config));
                        containerBuilder.RegisterModule(new AmadeusFlightStatusModule(config));
                        containerBuilder.RegisterModule(new TravelportFlightStatusModule(config));
                        containerBuilder.RegisterModule(new TravelfusionFlightStatusModule(config));
                        containerBuilder.RegisterModule(new AirGatewayFlightStatusModule(config));
                        containerBuilder.RegisterModule(new ExtraServiceManagementModule(config));
                        containerBuilder.RegisterModule(new PriceModule(config));
                        containerBuilder.RegisterModule(new BookingInfraModule());
                        containerBuilder.RegisterModule(new Co2EmissionsModule(config));
                        containerBuilder.RegisterModule(new ChangeExecutionModule(config));
                        containerBuilder.RegisterModule(new CancellationModule());
                        containerBuilder.RegisterModule(new ClassDropModule());

                        containerBuilder.RegisterType<Common.Host.AutofacResolver>().As<Common.Host.IResolver>();
                        containerBuilder.RegisterInstance(DateTimeProvider.Default).SingleInstance();
                        containerBuilder.RegisterType<DbSessionAccessor>().As<IDbSessionAccessor>().InstancePerLifetimeScope();
                        containerBuilder.RegisterType<TransactionManager>().As<ITransactionManager>().InstancePerLifetimeScope();
                    });

                var serviceCollection = builder.Services;
                serviceCollection
                    .AddTraceContext()
                    .AddTraceparentHeaderPropagation()
                    .AddLogging(config)
                    .AddCors()
                    .AddControllers()
                    .AddCancellationControllers()
                    .AddNewtonsoftJsonSerializer()
                    .AddHttpContextAccessor()
                    .AddEndpointsApiExplorer()
                    .AddAuths(config)
                    .AddMongo(config)
                    .AddRedis(config)
                    .AddRabbit(config)
                    .AddDefaultServiceContext()
                    .AddSwagger(config)
                    .AddApmMongo(config)
                    .AddApmMetricsCollector(config)
                    .AddElasticsearch(config)
                    .AddMemoryCache()
                    .AddFeatureServices(config)
                    .AddFeatureManagement(config);
                
                serviceCollection.AddProxy(c => c.AddRetryPolicyHandler());
                serviceCollection.AddScoped<IApiClientProxyFactory, ApiClientProxyFactory>();
                serviceCollection.Configure<ClassDropOptions>(config.GetSection(ClassDropOptions.Section));
                serviceCollection.Configure<ExcelOptions>(config.GetSection("Excel"));
                serviceCollection.Configure<BookingEventsOptions>(config.GetSection("BookingEvents"));
                serviceCollection.Configure<CTeleport.Services.Providers.Configuration.ProvidersOptions>(config.GetSection("Providers"));
                serviceCollection.Configure<GalileoOptions>(config.GetSection("Galileo"));
                serviceCollection.AddSingleton(AutoMapperConfig.InitializeMapper());
                serviceCollection.AddSingleton<IExcelClient, ExcelClient>();
                serviceCollection.AddSingleton<IBookingEventsClient, BookingEventsClient>();
                serviceCollection.AddSingleton<CTeleport.Services.Providers.Cache.IProvidersCache, CTeleport.Services.Providers.Cache.ProvidersCache>();
                serviceCollection.AddSingleton<Providers.Client.IProvidersClient, Providers.Client.ProvidersClient>();
                serviceCollection.AddSingleton<IMongoChangeHandler, MongoChangeHandler>();
                serviceCollection.AddSingleton<IMongoChangeHandler, BookingChangeHandler>();

                serviceCollection.AddBookingOrchestratorClient(config
                    .GetSection(CTeleport.Services.Booking.Configuration.OrchestratorOptions.OrchestratorSectionName)
                    .GetValue<string>(nameof(CTeleport.Services.Booking.Configuration.OrchestratorOptions.Url)));

                serviceCollection
                    .AddHostedService<SubscriptionsHostedService>()
                    .AddHostedService<DatabaseSeedHostedService>();

                serviceCollection.AddServiceMetrics(withInstanceIdTag: true);

                ConfigureChangeStream(serviceCollection);

                serviceCollection.AddApiVersioning(options =>
                {
                    options.AssumeDefaultVersionWhenUnspecified = true;
                    options.ApiVersionReader = new MediaTypeApiVersionReader();
                });

                serviceCollection.AddSingleton(config.GetSettings<Common.Authorization.Configuration.AuthzOptions>());
                serviceCollection.AddSingleton<IAuthzClient, AuthzClient>();
                serviceCollection.AddAuthService();
                serviceCollection.AddMessaging(config);

                serviceCollection
                    .AddHealthChecksWithPublisher()
                    .AddRabbit(config)
                    .AddCAP(config)
                    .AddFeatBit(config);

                serviceCollection
                    .AddValidatorsFromAssemblyContaining<CreateBookingRequestV2Validator>()
                    .AddValidatorsFromAssemblyContaining<Program>()
                    .AddFluentValidationAutoValidation(); // btw! Auto Validation not recommended anymore

                var application = builder.Build();

                if (application.Environment.IsDevelopment())
                    application.UseDeveloperExceptionPage();

                application.UseApm(config, new HttpDiagnosticsSubscriber(), new MongoDbDiagnosticsSubscriber());

                application
                    .UseSwagger()
                    .UseTraceContext()
                    .UseLogging()
                    .UseExceptionHandling()
                    .UseRouting()
                    .UseAuths()
                    .UseCors(policyBuilder => policyBuilder
                        .AllowAnyHeader()
                        .AllowAnyMethod()
                        .AllowAnyOrigin())
                    .UseEndpoints(endpoints =>
                    {
                        endpoints.MapHealthChecks();
                        endpoints.MapControllers();
                    });

                // Expose "/metrics" endpoint outside
                application.MapPrometheusScrapingEndpoint();

                var approvalQueueOptions = application.Services.GetRequiredService<ApprovalQueueOptions>();
                if (approvalQueueOptions.UseProxy)
                {
                    application.MapWhen(
                        c => approvalQueueOptions.ShouldProxy(c.Request.Path),
                        b => b.RunAuthProxy(c =>
                        {
                            c.Request.Path = approvalQueueOptions.GetProxyUrl(c.Request.Path);
                            return c.ForwardTo(approvalQueueOptions.Url).AddXForwardedHeaders().Send();
                        }, async context => approvalQueueOptions.GetAuthRequiredOption(context.Request.Path)));
                }

                await application.RunAsync();

                return 0;
            }
            catch (Exception ex)
            {
                await ServiceBootstrapExceptionHandler.PrintExceptionAsync(ex);
                return -1;
            }
            finally
            {
                Log.CloseAndFlush();
            }
        }

        private static void ConfigureChangeStream(IServiceCollection services)
        {
            services.AddHostedService(s => new GenericChangeStreamListener(
                    s.GetRequiredService<IMongoDatabase>(),
                    s.GetServices<IMongoChangeHandler>(),
                    s.GetRequiredService<ILogger>(),
                    s.GetRequiredService<Common.Redis.ILockFactory>(),
                    s.GetRequiredService<IRedisSafeOperationsService>())
                .Subscribe<Reservation>(BookingQueries.ReservationsName)
                .Subscribe<Booking.Models.Booking>(BookingQueries.NewBookingName));
        }
    }
}
