using System.ComponentModel.DataAnnotations;

namespace CTeleport.Api.Models
{
    public class SetTicketAsNonRefundableRequest : BaseModifyReservationRequestV2
    {
        /// <summary>
        /// Ticket number that is non refundable
        /// </summary>
        [Required]
        public string Number { get; set; }

        /// <summary>
        /// Reject reason
        /// </summary>
        [Required]
        public string Reason { get; set; }
    }
}