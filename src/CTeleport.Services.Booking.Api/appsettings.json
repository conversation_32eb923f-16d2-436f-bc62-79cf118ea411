{"Security": {"Auth0": {"ClientId": "B1ExeYqqeM9NLkvZQSCZ953GAGtB7unT,37Jo0TnyMzsUYOmXNYrQkuL4NDRf7uLQ,u5rrPA9uuqdl3SlX7Ppz4t8ZLRwon9vR,1Iifqr8tPL8f9uIiCgHdwNAyjNgYM1pW", "Domain": "c-teleport-dev.eu.auth0.com", "Namespace": "http://cteleport.com/"}, "ApiKeyAuthentication": {"Key": "secret-key"}}, "AirGateway": {"Url": "http://airgateway.app.infra-dev.cteleport.com:8100"}, "Authz": {"Url": "http://authz.app.infra-dev.cteleport.com:8100"}, "FareTermsApi": {"Url": "http://fare-terms.app.infra-dev.cteleport.com:8100"}, "PlacesApi": {"Url": "http://places.app.infra-dev.cteleport.com:8100", "MaxResults": 10}, "AirlinesApi": {"Url": "http://airline-settings.app.infra-dev.cteleport.com:8100"}, "FailedPNRQueue": {"Url": "http://localhost:5048", "UseProxy": true, "Proxy": [{"From": "/failed-pnr-queue/statistics", "To": "/statistics"}]}, "Orchestrator": {"Url": "http://localhost:5125"}, "Billing": {"Url": "http://billing.app.infra-dev.cteleport.com:8100", "UseProxy": true, "Proxy": [{"From": "/billing-settings", "To": "/settings"}, {"From": "/webhook/ecommpay", "To": "/webhook/ecommpay", "DisableAuth": true}, {"From": "/billing/statistics", "To": "/statistics"}]}, "Excel": {"Url": "http://localhost:5006"}, "BookingEvents": {"Url": "http://localhost:5012"}, "Providers": {"Url": "http://providers.app.infra-dev.cteleport.com:8100", "CacheTTLMinutes": 10}, "Galileo": {"Url": "http://localhost:5015"}, "Travelfusion": {"Url": "http://localhost:5074"}, "Amadeus": {"Url": "http://localhost:5035"}, "AirlineApi": {"Url": "http://airline.app.infra-dev.cteleport.com:8100"}, "ApprovalQueue": {"Url": "http://localhost:5041", "SkipApprovalRequiredFlow": false, "UseProxy": true, "Proxy": [{"From": "/approval-queue/status", "To": "/approval-queue/status", "DisableAuth": true}]}, "UsersApi": {"Url": "http://users.app.infra-dev.cteleport.com:8100"}, "MongoDb": {"ConnectionString": "mongodb://localhost:27017/?retryWrites=true", "Database": "CTeleport"}, "RabbitMq": {"Hosts": "localhost", "Username": "guest", "Password": "guest", "ExchangeName": "cap.hotels"}, "RawRabbit": {"Username": "guest", "Password": "guest", "VirtualHost": "/", "Port": 5672, "Hostnames": ["localhost"], "RequestTimeout": "00:00:10", "PublishConfirmTimeout": "00:00:10", "RecoveryInterval": "00:00:10", "PersistentDeliveryMode": true, "AutoCloseConnection": true, "AutomaticRecovery": true, "TopologyRecovery": true, "Exchange": {"Durable": true, "AutoDelete": false, "Type": "Topic"}, "Queue": {"AutoDelete": false, "Durable": true, "Exclusive": false}}, "Redis": {"ConnectionString": "localhost:6379", "UseSentinel": "false", "MasterName": "redis-sentinel", "MaxPoolSize": 500}, "Cache": {"FareRulesTtlInMinutes": 1440}, "Void": {"VoidSafetyTime": 60, "CustomVoidRules": {"1G.55EL": "NextWorkingDayMidnight", "1A.FLL1S21DX": "NextWorkingDayMidnight", "1A.FLL1S218J": "NextWorkingDayMidnight"}}, "Ticketing": {"BeforeDepartureTTL": 26, "ImmediateTicketingTTL": 3, "RetryIntervalAfterFailure": 60, "InstantTicketCarriers": ["B6", "FZ"], "InstantTicketCarriersPerProvider": {"AG": ["EK"]}, "FareMaskValidity": {"RefreshRequired": {"1G": ["EY"]}}}, "FareRefresh": {"OfficeHours": [7, 16], "Threshold": 1.0}, "TicketStateValidation": {"CooldownBeginsHoursBeforeDeparture": 1, "CooldownEndHoursAfterDeparture": 48}, "SlackNotifications": {"WebhookUrl": "*****************************************************************************"}, "Sentry": {"DSN": "https://a0f68733e32b48718b8d663f79dca61c:<EMAIL>/166193"}, "Serilog": {"MinimumLevel": "Debug"}, "Search": {"Url": "http://search.app.infra-dev.cteleport.com:8100", "UseProxy": true, "Proxy": [{"From": "/analytics", "To": "/analytics"}, {"From": "/search-analitics", "To": "/search-analitics"}, {"From": "/v1/flight_solution", "To": "/v1/flight_solution"}, {"From": "/v2/flight_solution", "To": "/v2/flight_solution", "ExceptActions": ["alternative"]}, {"From": "/v3/flight_solution", "To": "/v3/flight_solution", "ExceptActions": ["alternative/details"]}, {"From": "/v2/search", "To": "/v2/search"}, {"From": "/v3/search", "To": "/v3/search"}, {"From": "/change", "To": "/change"}]}, "Travelport": {"Url": "http://localhost:5081"}, "TravelportJson": {"Url": "http://localhost:5155"}, "Aws": {"PrimarySecretsRegion": "eu-central-1"}, "ClassDrop": {"Ticketed": {"AllowedCarriers": ["EK", "KL", "AF", "DL", "TK", "TP", "PS", "BT", "LO", "WY", "TG", "S7", "AV", "SQ", "BA", "KE", "OU", "UL", "AA", "QR", "KQ", "UA", "SK", "PR", "IB", "CX", "SV", "A3", "OA", "JU", "UX", "FB", "KC", "ET", "CI", "DH", "AY", "MS", "GP", "MH", "CZ"]}}, "TenantManagementService": {"Url": "http://localhost:5028"}, "ExtraServiceManagement": {"Url": "http://extra-service-management.app.infra-dev.cteleport.com:8100", "UseProxy": true, "Proxy": [{"From": "/extra-service-management/all", "To": "/all"}, {"From": "/extra-service-management/price/calculate", "To": "/price/calculate"}, {"From": "/extra-service-management/create", "To": "/create"}, {"From": "/extra-service-management/statistics", "To": "/statistics"}, {"From": "/extra-service-management/refund", "To": "/refund"}, {"From": "/extra-services", "To": "/extra-services"}]}, "BookingExport": {"TenantMapping": {"gmt*": {"PriceRoundToCeiling": true}, "nsb*": {"PriceRoundToCeiling": false}, "fugro*": {"PriceRoundToCeiling": false}, "ponant*": {"PriceRoundToCeiling": false}}, "Default": {"PriceRoundToCeiling": true}}, "Remarks": {"Aircrew": {"OSI": {"Default": "Must carry ID card and/or letter of authority"}}, "EnergySSRs": {"Default": [{"Type": "<PERSON><PERSON><PERSON>", "OSI": ["MUST CARRY OFFSHORE TRAVEL DOC"]}], "AI": [{"Type": "<PERSON><PERSON><PERSON>", "OSI": ["Marine booking"]}], "LH": [{"Type": "<PERSON><PERSON><PERSON>", "SSR": {"CKIN": "SEAMAN"}, "OSI": ["OFFSHORE/{Tenant.Name}/{Site.Name}/{Site.Location}"]}], "LX": [{"Type": "<PERSON><PERSON><PERSON>", "SSR": {"CKIN": "SEAMAN"}, "OSI": ["OFFSHORE/{Tenant.Name}/{Site.Name}/{Site.Location}"]}], "SN": [{"Type": "<PERSON><PERSON><PERSON>", "SSR": {"CKIN": "SEAMAN"}, "OSI": ["OFFSHORE/{Tenant.Name}/{Site.Name}/{Site.Location}"]}], "OS": [{"Type": "<PERSON><PERSON><PERSON>", "SSR": {"CKIN": "SEAMAN"}, "OSI": ["OFFSHORE/{Tenant.Name}/{Site.Name}/{Site.Location}"]}], "EK": [{"Type": "<PERSON><PERSON><PERSON>", "OSI": ["OFFSHORE/{Tenant.Name}/{Site.Name}"]}], "QR": [{"Type": "<PERSON><PERSON><PERSON>", "OSI": ["Must carry OFFSHORE TRAVEL DOC"]}], "KL": [{"Type": "<PERSON><PERSON><PERSON>", "OSI": ["ENERGY/{Site.Name}/{Site.Location}"]}], "AF": [{"Type": "<PERSON><PERSON><PERSON>", "OSI": ["ENERGY/{Site.Name}/{Site.Location}"]}], "GA": [{"Type": "<PERSON><PERSON><PERSON>", "OSI": ["OFFSHORE/{Site.Name}/{Site.Location}"]}], "SV": [{"Type": "<PERSON><PERSON><PERSON>", "OSI": ["OFFSHORE/{Site.Name}/{Site.Location}"]}], "KQ": [{"Type": "<PERSON><PERSON><PERSON>", "OSI": ["OFFSHORE/{Site.Name}/{Site.Location}"]}], "BA": [{"Type": "<PERSON><PERSON><PERSON>", "OSI": ["OFSH/{Site.Name}/{Site.Location}"]}], "IB": [{"Type": "<PERSON><PERSON><PERSON>", "OSI": ["OFSH/{Site.Name}/{Site.Location}"]}], "SQ": [{"Type": "<PERSON><PERSON><PERSON>", "OSI": ["VIEW SEAMEN OFFSHORE TRAVEL DOC"]}], "EY": [{"Type": "1A", "SSR": {"TLEX": "SEMN"}}, {"Type": "1G", "OSI": ["TLEX SEMN"]}], "LO": [{"Type": "1A", "OSI": ["SEMN"]}, {"Type": "1G", "OSI": ["SEMN"]}], "SK": [{"Type": "<PERSON><PERSON><PERSON>", "SSR": {"SEMN": "{Site.Name}"}, "OSI": ["SEMN {Site.Name}"]}], "OZ": [{"Type": "<PERSON><PERSON><PERSON>", "OSI": ["OFFSHORE/{Site.Name}/{Site.Location}"]}], "AC": [{"Type": "<PERSON><PERSON><PERSON>", "OSI": ["OFFSHORE/{Tenant.Name}/{Site.Name}/{Site.Location}"]}], "KM": [{"Type": "<PERSON><PERSON><PERSON>", "OSI": ["MAR01"]}], "TK": [{"Type": "<PERSON><PERSON><PERSON>", "OSI": ["Must present valid travel doc"]}], "A3": [{"Type": "<PERSON><PERSON><PERSON>", "OSI": ["Crew on duty"]}], "AZ": [{"Type": "<PERSON><PERSON><PERSON>", "OSI": ["OFFSHORE/{Site.Location}"]}]}, "SeamenSSRs": {"Default": [{"SSR": {"CKIN": "SEMN {Vessel.Name} REG {Vessel.Flag}"}, "OSI": ["SEMN {Vessel.Name} REG {Vessel.Flag}"]}], "AI": [{"Type": "<PERSON><PERSON><PERSON>", "OSI": ["Marine booking"]}], "BA": [{"Type": "<PERSON><PERSON><PERSON>", "SSR": {"SEMN": "MV {Vessel.Name} REG {Vessel.Flag}"}, "OSI": ["SEMN {Vessel.Name} REG {Vessel.Flag}"]}], "BR": [{"Type": "<PERSON><PERSON><PERSON>", "SSR": {"SEMN": "MV {Vessel.Name} REG {Vessel.Flag}"}, "OSI": ["SEAMAN {Vessel.Name} REG {Vessel.Flag}"]}], "AM": [{"Type": "<PERSON><PERSON><PERSON>", "SSR": {"SEMN": "MV {Vessel.Name} REG {Vessel.Flag}"}}], "BT": [{"Type": "<PERSON><PERSON><PERSON>", "SSR": {"SEMN": "{Vessel.Name}"}}], "CX": [{"Type": "<PERSON><PERSON><PERSON>", "OSI": ["SEAMAN"]}], "UX": [{"Type": "<PERSON><PERSON><PERSON>", "SSR": {"SEMN": "SEAMAN {Vessel.Name} REG {Vessel.Flag}"}, "OSI": ["SEAMAN"]}], "CZ": [{"Type": "<PERSON><PERSON><PERSON>", "SSR": {"SEMN": "{Vessel.Name}"}}], "EK": [{"Type": "<PERSON><PERSON><PERSON>", "SSR": {"SEMN": "{Vessel.Name} REG {Vessel.Flag}"}, "OSI": ["SEAMEN"]}], "EY": [{"Type": "<PERSON><PERSON><PERSON>", "SSR": {"SEMN": "{Vessel.Name} REG {Vessel.Flag}"}, "OSI": ["SEMN"]}], "SK": [{"Type": "<PERSON><PERSON><PERSON>", "SSR": {"SEMN": "{Vessel.Name}"}, "OSI": ["SEMN {Vessel.Name}"]}], "LO": [{"Type": "<PERSON><PERSON><PERSON>", "OSI": ["SEMN"]}], "PS": [{"Type": "<PERSON><PERSON><PERSON>", "SSR": {"CKIN": "SEMN {Vessel.Name}/{Vessel.Flag}", "SEMN": "ON DUTY"}, "OSI": ["SEMN {Vessel.Name}/{Vessel.Flag}/TA {Agency.IATA} {Agency.Name} {Agency.Phone}"]}], "SQ": [{"Type": "<PERSON><PERSON><PERSON>", "SSR": {"SEMN": "VIEW SEAMEN/OFF-SHORE TRVL DOC"}}], "HM": [{"Type": "<PERSON><PERSON><PERSON>", "SSR": {"SEMN": "{Vessel.Name} REG {Vessel.Flag}"}, "OSI": ["SEMN {Vessel.Name} REG {Vessel.Flag}"]}], "KE": [{"Type": "<PERSON><PERSON><PERSON>", "SSR": {"SEMN": "{Vessel.Name}"}}], "7С": [{"Type": "<PERSON><PERSON><PERSON>", "SSR": {"LANG": "GMT SEA PAX"}}], "KL": [{"Type": "<PERSON><PERSON><PERSON>", "SSR": {"SEMN": "{Vessel.Flag} FLAG {Vessel.Name} "}, "OSI": ["SEMN {Vessel.Name} REG {Vessel.Flag}", "MARINE", "TLPW.MARINE"]}], "AF": [{"Type": "<PERSON><PERSON><PERSON>", "SSR": {"SEMN": "{Vessel.Flag} FLAG {Vessel.Name} "}, "OSI": ["SEMN {Vessel.Name} REG {Vessel.Flag}", "MARINE", "TLPW.MARINE"]}], "DL": [{"Type": "<PERSON><PERSON><PERSON>", "SSR": {"SEMN": "{Vessel.Flag} FLAG {Vessel.Name} "}, "OSI": ["SEMN {Vessel.Name} REG {Vessel.Flag}", "TLPW.MARINE"]}], "OS": [{"Type": "<PERSON><PERSON><PERSON>", "SSR": {"CKIN": "SEAMAN"}, "OSI": ["SEMN {Vessel.Name} REG {Vessel.Flag}", "{Agency.Name} - tel {Agency.Phone} - IATA {Agency.IATA}", "SEAMAN MV {Vessel.Name}"]}, {"Type": "1A", "OSI": ["SEMN {Vessel.Name} REG {Vessel.Flag}", "{Agency.Name} - tel {Agency.Phone} - IATA {Agency.IATA}", "SEAMAN MV {Vessel.Name}"]}, {"Type": "1G", "SSR": {"CKIN": "SEAMAN"}, "OSI": ["SEMN {Vessel.Name} REG {Vessel.Flag}", "{Agency.Name} - tel {Agency.Phone} - IATA {Agency.IATA}", "SEAMAN MV {Vessel.Name}"]}], "SN": [{"Type": "<PERSON><PERSON><PERSON>", "SSR": {"CKIN": "SEAMAN"}, "OSI": ["SEMN {Vessel.Name} REG {Vessel.Flag}", "{Agency.Name} - tel {Agency.Phone} - IATA {Agency.IATA}", "SEAMAN MV {Vessel.Name}"]}, {"Type": "1A", "OSI": ["SEMN {Vessel.Name} REG {Vessel.Flag}", "{Agency.Name} - tel {Agency.Phone} - IATA {Agency.IATA}", "SEAMAN MV {Vessel.Name}"]}, {"Type": "1G", "SSR": {"CKIN": "SEAMAN"}, "OSI": ["SEMN {Vessel.Name} REG {Vessel.Flag}", "{Agency.Name} - tel {Agency.Phone} - IATA {Agency.IATA}", "SEAMAN MV {Vessel.Name}"]}], "LH": [{"Type": "<PERSON><PERSON><PERSON>", "SSR": {"CKIN": "SEAMAN"}, "OSI": ["SEMN {Vessel.Name} REG {Vessel.Flag}", "{Agency.Name} - tel {Agency.Phone} - IATA {Agency.IATA}", "SEAMAN MV {Vessel.Name}"]}, {"Type": "1A", "OSI": ["SEMN {Vessel.Name} REG {Vessel.Flag}", "{Agency.Name} - tel {Agency.Phone} - IATA {Agency.IATA}", "SEAMAN MV {Vessel.Name}"], "Dynamic": [{"Type": "SSR", "Carrier": "EW", "TypeInGds": "OTHS", "CategoryInGds": "OTHS", "Template": "MARINE FARE {Pcc}"}]}, {"Type": "1G", "SSR": {"CKIN": "SEAMAN"}, "OSI": ["SEMN {Vessel.Name} REG {Vessel.Flag}", "{Agency.Name} - tel {Agency.Phone} - IATA {Agency.IATA}", "SEAMAN MV {Vessel.Name}"], "Dynamic": [{"Type": "General", "Carrier": "EW", "TypeInGds": "<PERSON><PERSON><PERSON>", "CategoryInGds": "<PERSON><PERSON><PERSON>", "Template": "MARINE FARE {Pcc}"}]}], "LX": [{"Type": "<PERSON><PERSON><PERSON>", "SSR": {"SEMN": "HLDG ALL NEC DOCS", "CKIN": "SEAMAN"}, "OSI": ["{Agency.Name} - tel {Agency.Phone} - IATA {Agency.IATA}", "SEAMAN MV {Vessel.Name}"]}, {"Type": "1A", "SSR": {"SEMN": "HLDG ALL NEC DOCS"}, "OSI": ["{Agency.Name} - tel {Agency.Phone} - IATA {Agency.IATA}", "SEAMAN MV {Vessel.Name}"]}, {"Type": "1G", "SSR": {"SEMN": "HLDG ALL NEC DOCS", "CKIN": "SEAMAN"}, "OSI": ["{Agency.Name} - tel {Agency.Phone} - IATA {Agency.IATA}", "SEAMAN MV {Vessel.Name}"]}], "AC": [{"Type": "<PERSON><PERSON><PERSON>", "OSI": ["SEMN {Vessel.Name} REG {Vessel.Flag}", "{Agency.Name} - tel {Agency.Phone} - IATA {Agency.IATA}", "DEAL CIC 50/ITSEAMAN"]}], "UA": [{"Type": "<PERSON><PERSON><PERSON>", "OSI": ["SEMN {Vessel.Name} REG {Vessel.Flag}", "{Agency.Name} - tel {Agency.Phone} - IATA {Agency.IATA}", "SEE GG INT REG FOR CHANGES", "UA MARINE NETT TKT"]}], "AY": [{"Type": "<PERSON><PERSON><PERSON>", "OSI": ["MARINE FARES {Vessel.Name} {Vessel.Flag}"], "SSR": {"SEMN": "{Vessel.Flag} FLAG {Vessel.Name} "}}], "EW": [{"Type": "<PERSON><PERSON><PERSON>", "SSR": {"CKIN": "SEMN {Vessel.Name} REG {Vessel.Flag}", "OTHS": "MARINE FARE {Pcc}"}, "OSI": ["SEMN {Vessel.Name} REG {Vessel.Flag}", "{Agency.Name} - tel {Agency.Phone} - IATA {Agency.IATA}", "MARINE FARE {Pcc}"]}, {"Type": "1A", "SSR": {"CKIN": "SEMN {Vessel.Name} REG {Vessel.Flag}"}, "OSI": ["SEMN {Vessel.Name} REG {Vessel.Flag}", "{Agency.Name} - tel {Agency.Phone} - IATA {Agency.IATA}", "MARINE FARE {Pcc}"], "Dynamic": [{"Type": "SSR", "Carrier": "EW", "TypeInGds": "OTHS", "CategoryInGds": "OTHS", "Template": "MARINE FARE {Pcc}"}]}, {"Type": "1G", "SSR": {"CKIN": "SEMN {Vessel.Name} REG {Vessel.Flag}"}, "OSI": ["SEMN {Vessel.Name} REG {Vessel.Flag}", "{Agency.Name} - tel {Agency.Phone} - IATA {Agency.IATA}", "MARINE FARE {Pcc}"], "Dynamic": [{"Type": "General", "Carrier": "EW", "TypeInGds": "<PERSON><PERSON><PERSON>", "CategoryInGds": "<PERSON><PERSON><PERSON>", "Template": "MARINE FARE {Pcc}"}]}], "HX": [{"Type": "<PERSON><PERSON><PERSON>", "SSR": {"CKIN": "SEMN SEAMAN"}, "OSI": ["SEMN SEABOOK"]}]}, "CorporateCodeOSITemplates": {"A3": {"Required": false, "Template": "SRCLID-A3{Code}"}, "KL": {"Required": true, "Template": "OIN {Code}"}, "AF": {"Required": true, "Template": "OIN {Code}"}, "DL": {"Required": true, "Template": "OIN {Code}"}, "KQ": {"Required": true, "Template": "OIN {Code}"}, "CZ": {"Required": true, "Template": "OIN {Code}"}, "MU": {"Required": true, "Template": "OIN {Code}"}, "SK": {"Required": false, "Template": "CMP {Code}"}, "BA": {"Required": false, "Template": "SSRCLIDBAHK1/{Code}"}, "IB": {"Required": false, "Template": "SSRCLIDIBHK1/{Code}"}, "AA": {"Required": false, "Template": "SSRCLIDAAHK1/{Code}"}, "TK": {"Required": false, "Template": "SSRCLIDTKHK1/TK{Code}"}, "EK": {"Required": false, "Template": "DEAL {Code}"}, "QR": {"Required": false, "Template": "DC/{Code}"}, "EY": {"Required": false, "Template": "CPIN {Code}"}, "AZ": {"Required": false, "Template": "FQTB {Code}"}, "SQ": {"Required": false, "Template": "CP/{Code}"}, "LH": {"Required": false, "Template": "CP/LH{Code}"}, "NH": {"Required": false, "Template": "CP/LH{Code}"}, "AC": {"Required": false, "Template": "CP/LH{Code}"}, "CA": {"Required": false, "Template": "CP/LH{Code}"}, "EN": {"Required": false, "Template": "CP/LH{Code}"}, "UA": {"Required": false, "Template": "CP/LH{Code}"}, "EW": {"Required": false, "Template": "CP/LH{Code}"}, "SN": {"Required": false, "Template": "CP/LH{Code}"}, "LX": {"Required": false, "Template": "CP/LH{Code}"}, "OS": {"Required": false, "Template": "CP/LH{Code}"}, "BF": {"Required": false, "Template": "{Code}"}, "TX": {"Required": false, "Template": "{Code}"}, "TP": {"Required": false, "Template": "CP/CINL{Code}"}, "DY": {"Required": false, "Template": "NCA {Code}"}}}, "CustomFieldsMigration": {"OriginalDatabaseName": "CTeleport", "OriginalCollectionName": "tenant-customfields", "DestinationDatabaseName": "cteleport-tenant-management", "DestinationCollectionName": "tenant-customfields-groups"}, "Price": {"Url": "http://price.app.infra-dev.cteleport.com:8100"}, "Notifications": {"AdminBookingPageUrl": "https://admin-dev.cteleport.com/booking/{0}"}, "Refund": {"PriceLimitToForceManualRefundInEuro": 2000}, "Co2Emissions": {"Url": "http://localhost:5102"}, "FeatureManagement": {"VesselManagementSource": false}, "VesselsManagement": {"Url": "http://localhost:5149"}, "ChangeExecution": {"Url": "http://localhost:5053"}, "Elasticsearch": {"Url": "http://localhost:9200"}, "FeatBit": {"StreamingUri": "wss://api.cteleport.com/featbit-es", "Secret": "secret"}, "BookingSoonExpiration": {"TakeBookingsForLastDays": 93, "StartWindowForReservationsInHours": 48, "EndWindowForReservationsInHours": 72}, "HealthChecks": {"Rabbit": {"Enabled": true, "Tags": ["live"]}, "CAP": {"Enabled": true}, "FeatBit": {"Enabled": true}}, "ElasticApm": {"Enabled": false}}