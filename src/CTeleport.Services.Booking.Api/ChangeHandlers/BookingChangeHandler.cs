using System.Threading.Tasks;
using CTeleport.Services.Helpers;
using CTeleport.Common.Messaging.Services;
using CTeleport.Messages.Events.Bookings;
using CTeleport.Services.Booking.Api.ChangeHandlers.Interfaces;
using CTeleport.Services.Booking.Models;
using CTeleport.Services.Booking.Repositories.Queries;
using MongoDB.Bson;
using MongoDB.Driver;
using Serilog;

namespace CTeleport.Services.Booking.Api.ChangeHandlers
{
    public class BookingChangeHandler : IMongoChangeHandler
    {
        private readonly IMessageDispatcher _messageDispatcher;
        private readonly ILogger _logger;

        public BookingChangeHandler(IMessageDispatcher messageDispatcher, ILogger logger)
        {
            _messageDispatcher = messageDispatcher;
            _logger = logger;
        }

        public async Task Handle(ChangeStreamDocument<BsonDocument> change)
        {
            var bookingId = GetBookingId(change);
            if (string.IsNullOrWhiteSpace(bookingId))
            {
                return;
            }

            var message = new TrackingBookingUpdated
            {
                BookingId = bookingId,
                TenantId = GetTenantId(change),
                UpdatedAt = change.ClusterTime.ToDateTime()
            };
            _logger.Information("BookingChangeHandler: {bookingId}, {message}, {collectionName}", bookingId, message,
                change.CollectionNamespace.CollectionName);
            await _messageDispatcher.DispatchAsync(message);
        }

        private static string GetTenantId(ChangeStreamDocument<BsonDocument> change)
        {
            var fieldName = change.CollectionNamespace.CollectionName == BookingQueries.NewBookingName
                ? nameof(CTeleport.Services.Booking.Models.Booking.TenantId)
                : nameof(Reservation.TenantId); 
            return change.FullDocument?.GetValue(fieldName)?.AsString;
        }

        private static string GetBookingId(ChangeStreamDocument<BsonDocument> change)
        {
            var collectionName = change.CollectionNamespace.CollectionName;
            string bookingId = collectionName == BookingQueries.NewBookingName
                ? change.DocumentKey["_id"].AsString
                : collectionName == BookingQueries.ReservationsName
                    ? change.FullDocument?.GetValue(nameof(Reservation.BookingId)).AsString
                    : null;

            return bookingId;
        }
    }
}