using CTeleport.Api.Models;
using FluentValidation;
// ReSharper disable UnusedType.Global

namespace CTeleport.Services.Booking.Api.Validators;

public class GetExtendedReportRequestValidator : AbstractValidator<GetExtendedReportRequest>
{
    public GetExtendedReportRequestValidator()
    {
        RuleFor(r => r.Limit).GreaterThanOrEqualTo(0).LessThanOrEqualTo(GetExtendedReportRequest.MaxLimit);
        RuleFor(r => r.Offset).GreaterThanOrEqualTo(0);
    }
}