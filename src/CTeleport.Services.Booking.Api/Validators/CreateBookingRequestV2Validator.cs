using CTeleport.Api.Models;
using FluentValidation;

namespace CTeleport.Services.Booking.Api.Validators
{
    public class CreateBookingRequestV2Validator : AbstractValidator<CreateBookingRequestV2>
    {
        public CreateBookingRequestV2Validator()
        {
            RuleFor(i => i.FlightSolutionId)
                .NotNull()
                .NotEmpty();
            
            RuleFor(i => i.Metadata)
                .SetValidator(new MetadataInputModelValidator());
            
            RuleFor(i => i.Passenger)
                .NotNull()
                .NotEmpty()
                .SetValidator(new PassengerDetailsValidator())
                .WithMessage("PassengerDetails has to be specified");

            RuleForEach(x => x.CustomFields).ChildRules(child =>
            {
                child.RuleFor(i => i.Key)
                    .NotNull()
                    .NotEmpty();

                child.RuleFor(i => i.Value)
                    .NotNull()
                    .NotEmpty();
            });

            RuleFor(i => i.PaymentMethodType)
                .IsInEnum();

            RuleForEach(x => x.FrequentFlyerNumbers).ChildRules(child =>
            {
                child.RuleFor(i => i.Key)
                    .NotNull()
                    .NotEmpty();

                child.RuleFor(i => i.Value).ChildRules(val =>
                {
                    val.RuleFor(i => i.Code)
                        .NotNull()
                        .NotEmpty();

                    val.RuleFor(i => i.Number)
                        .NotNull()
                        .NotEmpty();
                });
            });

            RuleForEach(x => x.Ancillaries).ChildRules(child =>
            {
                child.RuleFor(i => i.Key)
                    .NotNull()
                    .NotEmpty();
            });
        }
    }
}
