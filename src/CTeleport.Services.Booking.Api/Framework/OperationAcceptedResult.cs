using System;
using System.Threading.Tasks;

using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace CTeleport.Api.Framework
{
    public class OperationAcceptedResult : JsonResult
    {
        private string operationId;
        private string resourceId;
        private string resourceEndpoint;

        public OperationAcceptedResult(string operationId, string resourceId = null, string resourceEndpoint = null)
            : base(null)
        {
            if (string.IsNullOrWhiteSpace(operationId))
            {
                throw new ArgumentNullException(nameof(operationId));
            }

            this.operationId = operationId;
            this.resourceId = resourceId;
            this.resourceEndpoint = resourceEndpoint;
        }

        public override Task ExecuteResultAsync(ActionContext context)
        {
            this.StatusCode = StatusCodes.Status202Accepted;

            this.Value = new
            {
                OperationId = operationId,
                ResourceId = resourceId
            };

            context.HttpContext.Response.Headers.Add("X-Operation-Id", operationId);
            context.HttpContext.Response.Headers.Add("X-Operation", $"v2/operations/{operationId}");

            if (!String.IsNullOrWhiteSpace(resourceId))
                context.HttpContext.Response.Headers.Add("X-Resource-Id", resourceId);

            if (!String.IsNullOrWhiteSpace(resourceEndpoint))
                context.HttpContext.Response.Headers.Add("X-Resource", resourceEndpoint);

            return base.ExecuteResultAsync(context);
        }
    }
}