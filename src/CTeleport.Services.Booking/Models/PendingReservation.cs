using System;
using System.Collections.Generic;
using CTeleport.Common.Helpers;
using CTeleport.Services.Booking.Shared.Enums;
using MongoDB.Bson.Serialization.Attributes;

namespace CTeleport.Services.Booking.Models
{
    public class PendingReservation
    {
        [BsonId]
        public string ReservationId { get; set; }

        /// <summary>
        /// Record Locators.
        /// </summary>
        public Dictionary<string, string> Locators { get; set; }

        /// <summary>
        /// Provider Source.
        /// </summary>
        public string Source { get; set; }

        /// <summary>
        /// Current state.
        /// </summary>
        public ProviderReservationState State { get; set; }

        /// <summary>
        /// Date and time when the reservation enter in Pending status, UTC.
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// Date and time until this reservation should be checked.
        /// </summary>
        public DateTime ExpireAt { get; set; }

        /// <summary>
        /// Date and time when the reservation status was checked, UTC.
        /// </summary>
        public DateTime? CheckedAt { get; set; }

        /// <summary>
        /// Indicates whether the current pending reservation record is expired.
        /// </summary>
        public bool IsExpired { get { return DateTimeProvider.Default.UtcNow > ExpireAt; } }
    }
}