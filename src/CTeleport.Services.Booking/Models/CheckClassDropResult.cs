namespace CTeleport.Services.Booking.Models
{
    public class CheckClassDropResult
    {
        /// <summary>
        /// True, when class drop is possible for given reservation
        /// </summary>
        public bool IsPossible { get; set; }

        /// <summary>
        /// True, when class drop is allowed for given reservation
        /// </summary>
        public bool IsAllowed { get; set; }
        
        /// <summary>
        /// Reason of not possible class drop 
        /// </summary>
        public string NotPossibleReason { get; set; }

        /// <summary>
        /// Originally booked net ticket price
        /// </summary>
        public decimal PriceOriginal { get; set; }

        /// <summary>
        /// Currently held net ticket price
        /// </summary>
        public decimal PriceCurrent { get; set; }

        /// <summary>
        /// Net ticket price according to current price quotation
        /// </summary>
        public decimal Price { get; set; }

        /// <summary>
        /// Key of current price quotation from the Provider service
        /// </summary>
        public string ProviderKey { get; set; }
        
        /// <summary>
        /// If reservation has tickets
        /// </summary>
        public bool IsTicketed { get; internal set; }

        /// <summary>
        /// Use session or not.
        /// </summary>
        public bool UseSession { get; set; }
    }
}