using System.Collections.Generic;
using System.Linq;

namespace CTeleport.Services.Booking.Models
{
    /// <summary>
    /// Can confirm segments after schedule changed
    /// </summary>
    public class CanConfirmSegmentsCheck
    {
        /// <summary>
        /// Confirmation is allowed
        /// </summary>
        public bool Allowed => !AlreadyDeparted && !HasUnconfirmedSegments && Changes.All(c => !c.FlightNumberChanged && !c.FlightTimeShiftedTooFar);

        public ICollection<FlightChange> Changes { get; set; }

        /// <summary>
        /// Flight is already departed
        /// </summary>
        public bool AlreadyDeparted { get; set; }

        /// <summary>
        /// Reservation has unconfirmed segments
        /// </summary>
        public bool HasUnconfirmedSegments { get; set; }
    }
}