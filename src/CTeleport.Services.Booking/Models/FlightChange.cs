namespace CTeleport.Services.Booking.Models
{
    /// <summary>
    /// Flight change details
    /// </summary>
    public class FlightChange
    {
        public bool FlightNumberChanged { get; set; }

        public bool FlightTimeShiftedTooFar { get; set; }

        public bool IsFlightTimeChanged { get; set; }
        
        public string Carrier { get; set; }

        public string FlightNumber { get; set; }

        public string Departure { get; set; }

        public string DepartureDate { get; set; }
        
        public int DepartureShiftMinutes { get; set; }

        public string Arrival { get; set; }

        public string ArrivalDate { get; set; }

        public int ArrivalShiftMinutes { get; set; }
        
        /// <summary>
        /// Previous confirmed departure date. 
        /// </summary>
        public string PreviousDepartureDate { get; set; }
        
        /// <summary>
        /// Previous confirmed departure local time. 
        /// </summary>
        public string PreviousDepartureTime { get; set; }

        /// <summary>
        /// Is departure date too far from current date. 
        /// </summary>
        public bool IsDepartureDateTooFar { get; set; }
    }
}