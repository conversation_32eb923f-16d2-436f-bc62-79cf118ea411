using CTeleport.Services.Booking.Enums;
using System;
using System.Collections.Generic;

namespace CTeleport.Services.Booking.Models
{
    public class Ticket
    {
        /// <summary>
        /// Ticket number
        /// </summary>
        public string Number { get; set; }

        /// <summary>
        /// Ticket state
        /// </summary>
        public TicketState State { get; set; }

        /// <summary>
        /// Ticket No-show flag
        /// </summary>
        public bool NoShow { get; set; }

        /// <summary>
        /// Ticket price specifications
        /// </summary>
        public TicketPrice Price { get; set; }

        /// <summary>
        /// Ticket refund specifications
        /// </summary>
        public TicketRefund Refund { get; set; }

        /// <summary>
        /// Original ticket issue date in yyyy-MM-dd format
        /// </summary>
        /// <remarks>
        /// Highly important for refunds, must be actual issue date in BSP timezone
        /// </remarks>
        public string IssueDate { get; set; }

        /// <summary>
        /// Latest date and time for voiding, UTC
        /// </summary>
        public DateTime LastVoidAt { get; set; }

        /// <summary>
        /// Ticket issue date and time, UTC
        /// </summary>
        public DateTime IssuedAt { get; set; }

        /// <summary>
        /// Ticket voided date and time, UTC
        /// </summary>
        public DateTime? VoidedAt { get; set; }

        /// <summary>
        /// Ticket refunded date and time, UTC
        /// </summary>
        public DateTime? RefundedAt { get; set; }

        /// <summary>
        /// Ticket reissue date and time, UTC
        /// </summary>
        public DateTime? ReissuedAt { get; set; }

        /// <summary>
        /// Previous (exchanged) ticket number
        /// </summary>
        public string ReissuedFrom { get; set; }

        /// <summary>
        /// Invoices numbers this ticket appears in
        /// </summary>
        [Obsolete]
        public ICollection<string> InvoiceNumbers { get; set; }

        /// <summary>
        /// Credit note numbers this ticket appears in
        /// </summary>
        [Obsolete]
        public ICollection<string> CreditNoteNumbers { get; set; }

        /// <summary>
        /// If true, the Ticket can't be refunded automatically
        /// </summary>
        public bool ProhibitAutoRefund { get; set; }

        /// <summary>
        /// If true, the Ticket is non refundable. Reject reason stored in refunds queue.
        /// </summary>
        public bool NonRefundable { get; set; }

        /// <summary>
        /// Funding source
        /// </summary>
        public string FundingSource { get; set; }
    }
}