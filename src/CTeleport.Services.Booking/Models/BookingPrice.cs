using System;

namespace CTeleport.Services.Booking.Models
{
    public class BookingPrice
    {
        /// <summary>
        /// Total price, in target currency. Includes rounding margin, if rounding is applied
        /// </summary>
        public decimal Total { get; set; }

        /// <summary>
        /// Price per mile, in target currency
        /// </summary>
        public decimal PerMile { get; set; }

        /// <summary>
        /// Markup, in target currency
        /// </summary>
        public decimal Markup { get; set; }

        /// <summary>
        /// Kickback, in target currency
        /// </summary>
        public decimal Kickback { get; set; }

        /// <summary>
        /// Target currency
        /// </summary>
        public string Currency { get; set; }
    }
}