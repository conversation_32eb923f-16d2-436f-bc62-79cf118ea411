using CTeleport.Services.Search.Shared.Models;
using BookingSaga = CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Booking;

namespace CTeleport.Services.Booking.Models;

public class BookingApprovedRequest
{
    /// <summary>
    /// Request Id
    /// </summary>
    public string RequestId { get; init; }
    
    /// <summary>
    /// Tenant Id
    /// </summary>
    public string TenantId { get; init; }
    
    /// <summary>
    /// Identity of the User that approved the booking
    /// </summary>
    public Messages.Commands.Models.User ApprovedBy { get; init; }
    
    /// <summary>
    /// Booking Saga
    /// </summary>
    public BookingSaga BookingSaga { get; init; }
    
    /// <summary>
    /// Booking to be approved
    /// </summary>
    public Booking Booking { get; init; }
    
    /// <summary>
    /// Approved flight solution. Can be null.
    /// If is different from OriginalSolution it its the repriced solution that was approved.
    /// </summary>
    public FlightSolution ApprovedSolution { get; init; }
    
    /// <summary>
    /// Original Booking flight solution
    /// </summary>
    public FlightSolution OriginalSolution { get; init; }
    
    /// <summary>
    /// List of Booking reservations
    /// </summary>
    public Reservation[] Reservations { get; init; }
}
