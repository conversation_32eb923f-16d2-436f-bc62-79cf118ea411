using System;
using System.Collections.Generic;
using CTeleport.Api;
using CTeleport.Services.Booking.Enums;
using CTeleport.Services.Booking.Shared.Models;
using CTeleport.Services.Helpers;
using CTeleport.Services.Search.Shared.Models;
using CTeleport.Services.Search.Shared.Models.ProviderTerms;
using Segment = CTeleport.Services.Booking.Shared.Models.Segment;

namespace CTeleport.Services.Booking.Models
{
    public class Reservation : IUpdateTrackingEntity
    {
        /// <summary>
        /// Reservation id
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// Reservation id
        /// </summary>
        public string BookingId { get; set; }

        /// <summary>
        /// Reservation passenger
        /// </summary>
        public ReservationPassenger Passenger { get; set; }

        /// <summary>
        /// Reservation state
        /// </summary>
        public ReservationState State { get; set; }

        /// <summary>
        /// Irregularities registered for reservation, if any
        /// </summary>
        public ReservationIrregularities Irregularities { get; set; }

        /// <summary>
        /// Identifier of PNR source, the combination of GDS and PCC codes
        /// e.g. for C Teleport Galileo PCC in LV: "1G.8WG0"
        /// </summary>
        public string Source { get; set; }

        /// <summary>Funding source</summary>
        public string FundingSource { get; set; }

        /// <summary>
        /// Reservation locators, including supplier locators.
        /// Locators variate for providers
        /// 1G: AirReservation, UniversalRecord, Provider
        /// </summary>
        public Dictionary<string, string> Locators { get; set; }

        /// <summary>
        /// Dictionary of supplier locators
        /// </summary>
        public Dictionary<string, string> SupplierLocators { get; set; }

        /// <summary>
        /// Refresh fare scheduled date and time, UTC
        /// </summary>
        public DateTime? RefreshFareAt { get; set; }

        /// <summary>
        /// Current reservation fare specifications
        /// </summary>
        public Fare Fare { get; set; }

        /// <summary>
        /// Original reservation fare specifications. 
        /// If null, Fare property contains original fare specifications 
        /// </summary>
        public Fare OriginalFare { get; set; }

        /// <summary>
        /// All fare changes 
        /// </summary>
        public ICollection<FareChange> FareChanges { get; set; }

        /// <summary>
        /// Reservation price specifications including margins, markups, total price and currency conversion rate (when applicable)
        /// </summary>
        public ReservationPrice Price { get; set; }

        /// <summary>
        /// Reservation refund specifications including margins, markups, total refund amount and currency conversion rate (when applicable)
        /// </summary>
        public ReservationRefund Refund { get; set; }

        /// <summary>
        /// Baggage allowance details
        /// </summary>
        [Obsolete]
        public BaggageAllowance Baggage { get; set; }

        /// <summary>
        /// Baggage allowance details dictionary with O-D as key
        /// </summary>
        public IDictionary<string, BaggageAllowance> BaggageAllowances { get; set; }

        /// <summary>
        /// Fare rules dictionary with O-D pair as a key and MD5 hash for IDs
        /// </summary>
        public  Dictionary<string, List<string>> FareRulesIds { get; set; }
        
        /// <summary>
        /// Fare rules dictionary with O-D pair as a key
        /// <remarks>The property will be ignored from saving to storage.</remarks>
        /// </summary>
        public Dictionary<string, List<FareRuleSection>> FareRules { get; set; }

        /// <summary>
        /// MD5 hash for the text from 16 category
        /// </summary>
        public List<string> FareRuleCat16Ids { get; set; }

        /// <summary>
        /// First segment departure date and time, UTC
        /// </summary>
        public DateTime DepartureAt { get; set; }

        /// <summary>
        /// Total travel time for each leg in a route, in minutes
        /// </summary>
        public ICollection<int> LegDurations { get; set; }

        //TODO DataBase initializer
        /// <summary>
        /// Collection of flight legs
        /// </summary>
        public ICollection<Leg> Legs { get; set; }

        /// <summary>
        /// Collection of segments grouped by leg
        /// </summary>
        public ICollection<ICollection<Segment>> LegSegments { get; set; }

        /// <summary>
        /// Ticketing scheduled date and time, UTC
        /// </summary>
        public DateTime? TicketingAt { get; set; }

        /// <summary>
        /// Flag indicating that ticketing failed
        /// </summary>
        public bool? TicketingFailed { get; set; }

        /// <summary>
        /// Collection of tickets
        /// </summary>
        public ICollection<Ticket> Tickets { get; set; }

        /// <summary>
        /// Reservation's tickets changes 
        /// </summary>
        public ICollection<TicketChanges> Changes { get; set; }

        /// <summary>
        /// Date and time when this reservation was created, UTC
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// Date and time when this reservation was updated, UTC
        /// </summary>
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// Date and time when this reservation was cancelled, UTC
        /// </summary>
        public DateTime? CancelledAt { get; set; }

        /// <summary>
        /// Tenant id
        /// </summary>
        public string TenantId { get; set; }

        /// <summary>
        /// If true, checks for class drop will be run. 
        /// </summary>
        public bool ClassDropIsAllowed { get; set; }

        /// <summary>
        /// Indicates whether reservation can be cancelled (technically supported by provider)
        /// </summary>
        public bool CanCancel { get; set; }

        /// <summary>
        /// Indicates whether this reservation has a ticket associated or not. (If Ticketless==true, no ticket number is expected)
        /// </summary>
        public bool Ticketless { get; set; }

        /// <summary>
        /// Invoices numbers this reservation appears in
        /// </summary>
        public ICollection<string> InvoiceNumbers { get; set; }

        /// <summary>
        /// Credit note numbers this reservation appears in
        /// </summary>
        public ICollection<string> CreditNoteNumbers { get; set; }

        /// <summary>
        /// Custom calculated fields
        /// </summary>
        public Dictionary<string, string> Metadata { get; set; }

        /// <summary>
        /// Full cancellation timeline.
        /// </summary>
        public List<ConditionsTimespan> CancellationTimeline { get; set; }

        /// <summary>
        /// Full changes timeline.
        /// </summary>
        public List<ConditionsTimespan> ChangeTimeline { get; set; }
        
        /// <summary>
        /// Full changes timeline for partially used ticket.
        /// Can be null if the ticket has no promised conditions for changing partially used ticket.
        /// </summary>
        public List<ConditionsTimespan>? PartiallyUsedChangeTimeline { get; set; }

        /// <summary>
        /// SearchJob Metadata.
        /// </summary>
        public SearchJobMetadata SearchJobMetadata { get; set; }

        /// <summary>
        /// Indicates whether reservation is virtual
        /// </summary>
        public bool IsVirtual { get; set; }

        /// <summary>
        /// Reference to original reservation id (virtual...)
        /// </summary>
        public string OriginalReservationId { get; set; }

        /// <summary>
        /// Indicates whether reservation is approval required
        /// </summary>
        public bool ApprovalRequired { get; set; }

        /// <summary>
        /// Indicates whether reservation is awaiting payment confirmation (3DS flow with credit card)
        /// </summary>
        public bool PaymentRequired { get; set; }

        /// <summary>
        /// array of loyalty program info
        /// </summary>
        public List<FrequentFlyerNumber> FrequentFlyerNumbers { get; set; }
        
        /// <summary>
        /// Selected ancillaries (extra baggage etc.)
        /// </summary>
        public List<Ancillary> Ancillaries { get; set; }
    }

    /// <summary>
    /// Remove after migration for CT-2043 completed
    /// </summary>
    public class MigrationReservation
    {
        /// <summary>
        /// Reservation id
        /// </summary>
        public string Id { get; set; }

        public Locators Locators { get; set; }
    }

    public static class ReservationExt
    {
        public static string GetProviderCode(this Reservation reservation)
        {
            return LocatorsHelper.GetProviderCode(reservation.Locators);
        }

        public static string GetMainRecord(this Reservation reservation)
        {
            return LocatorsHelper.GetMainRecord(reservation.Locators);
        }
    }
}