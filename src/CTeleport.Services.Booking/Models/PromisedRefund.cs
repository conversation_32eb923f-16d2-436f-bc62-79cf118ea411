namespace CTeleport.Services.Booking.Models;

/// <summary>
/// Promised/estimated values for refund. Shown/Promised to user before actual refund is processed.  
/// </summary>
public class PromisedRefund
{
    /// <summary>
    /// Refund fee. Calculated based on rules by fare-terms service.   
    /// </summary>
    public decimal Fee { get; set; }
    
    /// <summary>
    /// Fee currency
    /// </summary>
    public string FeeCcy { get; set; }
}