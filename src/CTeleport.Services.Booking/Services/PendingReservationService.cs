using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CTeleport.Common.Exceptions;
using CTeleport.Common.Helpers;
using CTeleport.Services.Booking.Core;
using CTeleport.Services.Booking.Models;
using CTeleport.Services.Booking.Repositories;
using CTeleport.Services.Booking.Shared.Enums;

namespace CTeleport.Services.Booking.Services
{
    public class PendingReservationService : IPendingReservationService
    {
        private readonly IPendingReservationRepository _pendingReservationRepository;

        public PendingReservationService(IPendingReservationRepository pendingReservationRepository)
        {
            _pendingReservationRepository = pendingReservationRepository;
        }

        public async Task AddPendingReservationAsync(PendingReservation pendingReservation)
        {
            var pReservvation = await _pendingReservationRepository.GetAsync(pendingReservation.ReservationId);

            if (pReservvation != null)
            {
                throw new ValidationException(OperationCodes.Error, $"Pending reservation record already exists: {pendingReservation.ReservationId}");
            }

            await _pendingReservationRepository.AddAsync(pendingReservation);
        }

        public async Task<IReadOnlyCollection<PendingReservation>> GetPendingReservationsAsync(IEnumerable<string> ids)
        {
            var idsList = ids.ToList();
            if (!idsList.Any())
                return Enumerable.Empty<PendingReservation>().ToList();

            var reservations = await _pendingReservationRepository.GetAllAsync(
                r => idsList.Contains(r.ReservationId),
                idsList.Count);

            return reservations.ToArray();
        }

        public async Task<ICollection<PendingReservation>> GetActivePendingReservationsAsync(int limit = 10)
        {
            var pReservations = await _pendingReservationRepository.GetAllAsync(r =>
                r.State == ProviderReservationState.Pending,
                limit);

            return pReservations.ToArray();
        }

        public Task UpdatePendingReservationAsync(PendingReservation pendingReservation)
        {
            pendingReservation.CheckedAt = DateTimeProvider.Default.UtcNow;
            return _pendingReservationRepository.UpdateAsync(pendingReservation);
        }
    }
}