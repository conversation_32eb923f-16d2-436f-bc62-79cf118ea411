using CTeleport.Messages;
using CTeleport.Messages.Commands.Reservations;
using CTeleport.Messages.Events.Bookings;
using CTeleport.Messages.Events.Changes;
using CTeleport.Messages.Events.Reservations;
using CTeleport.Services.Booking.Core.Dto;
using CTeleport.Services.Booking.Core.Interfaces;
using CTeleport.Services.Booking.Enums;
using CTeleport.Services.Booking.Helpers;
using CTeleport.Services.Booking.Models;
using CTeleport.Services.Booking.Services.Interfaces;
using CTeleport.Services.Helpers;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CTeleport.Services.Booking.Services.PnrEventProcessingServices
{
    /// <summary>
    /// This abstract class contains common logic for Amadeus and Galileo PNR processing event.
    /// </summary>
    public abstract class PnrEventProcessingServiceBase
    {
        private readonly IProviderReservationInfoService _providerReservationService;
        private readonly IBookingMetricsService _metricsService;
        private readonly IBookingService _bookingService;
        
        protected readonly ILogger Logger;

        public PnrEventProcessingServiceBase(
            IProviderReservationInfoService providerReservationService,
            IBookingMetricsService metricsService,
            IBookingService bookingService,
            ILogger logger)
        {
            _providerReservationService = providerReservationService;
            _metricsService = metricsService;
            _bookingService = bookingService;
            Logger = logger;
        }

        protected async Task<IMessage> SetTicketingTimeAsync(string locator, DateTime newTicketingAt, Reservation reservation)
        {
            var logger = Logger
                .ForContext("BookingId", reservation.BookingId)
                .ForContext("ReservationId", reservation.Id)
                .ForContext("TenantId", reservation.TenantId);
            
            logger.Information("Handling PNRPlacedOnQueue event for {Locator} with new ticketing time {TicketingTime}", locator, newTicketingAt);

            var reservationHasTickets = (reservation.Tickets?.Count ?? 0) > 0;
            if (reservationHasTickets)
            {
                logger.Information("Skipped ticketing time update for {Locator}. Reservation has ticket(s)", locator);
                return default;
            }

            var skipUpdate = reservation.TicketingAt <= newTicketingAt.AddHours(-1);
            if (skipUpdate)
            {
                logger.Information("Skipped ticketing time update for {Locator}. Ticketing scheduled at {CurrentTicketingTime}", locator, reservation.TicketingAt);
                return default;
            }

            var ticketingTime = new DateTime(Math.Max(newTicketingAt.AddHours(-2).Ticks, DateTime.UtcNow.Ticks), DateTimeKind.Utc);

            logger.Information("Updating ticketing time for {Locator} from {OriginalTicketingTime} to {NewTicketingTime}",
                locator, reservation.TicketingAt, ticketingTime);

            await _bookingService.SetTicketingTimeAsync(reservation.Id, ticketingTime);
            
            return new TicketingAtUpdated
            {
                BookingId = reservation.BookingId,
                ReservationId = reservation.Id,
                TenantId = reservation.TenantId,
                TicketingAt = ticketingTime
            };
        }

        protected async Task<List<IMessage>> CreateTimeChangedMessagesAsync(PNRPlacedOnQueue @event, Reservation reservation, bool tryNewTimeConfirmation)
        {
            var logger = Logger
                .ForContext("BookingId", reservation.BookingId)
                .ForContext("ReservationId", reservation.Id)
                .ForContext("TenantId", reservation.TenantId);
            
            if (reservation.State == ReservationState.Cancelled)
            {
                logger.Information("Skip schedule change confirmation for cancelled reservation {Locator}", @event.Locator);
                return new List<IMessage>(0);
            }

            var providerCode = LocatorsHelper.GetProviderCode(reservation.Locators);
            logger.Information("Creating time changed event messages for reservation {Locator} (TenantId: {TenantId}, Source: {Source})", 
                providerCode, reservation.TenantId, reservation.Source);

            var request = new ProviderRetrieveReservationRequest
            {
                Source = reservation.Source,
                Locators = reservation.Locators
            };

            var response = await _providerReservationService.GetReservationAsync(request);

            if (response == null)
            {
                logger.Warning("Failed to retrieve Reservation with locator {Locator}. Null response", @event.Locator);
                return new List<IMessage>(0);
            }

            _metricsService.IncrementProviderValidationRequestCounter(
                reservation?.TenantId ?? string.Empty, reservation?.Source ?? string.Empty, response?.HasError ?? false);

            if (response.HasError)
            {
                logger.Warning("Reservation with locator {Locator} Not Found {@Error}", @event.Locator, response.Error);
                return new List<IMessage>(0);
            }

            var messageList = new List<IMessage>();
            var segmentsNew = response.FlightReservation.Segments.OrderBy(s => s.DepartureTimestampUtc).ToList();
            var segmentStatuses = response.FlightReservation.Segments.Select(s => s.Status).Distinct().ToList();
            var canConfirmSegmentsCheck = ReservationHelper.CanConfirmSegments(reservation, segmentsNew, segmentStatuses);

            if (canConfirmSegmentsCheck.AlreadyDeparted)
            {
                logger.Information("Skip schedule change confirmation for {Locator} because flight already departed",
                    LocatorsHelper.GetProviderCode(reservation.Locators));

                return new List<IMessage>(0);
            }

            messageList.AddRange(CreateFlightTimeChangedEvents(reservation, ReservationHelper.GetFlightChanges(reservation, segmentsNew)));

            if (tryNewTimeConfirmation)
            {
                var confirmationResultEvent = canConfirmSegmentsCheck.Allowed
                    ? new ConfirmChangedSegments { ReservationId = reservation.Id }
                    : CreateChangedSegmentsConfirmationRejectedEvent(reservation, canConfirmSegmentsCheck, segmentsNew);

                messageList.Add(confirmationResultEvent);
            }

            return messageList;
        }

        private IMessage CreateChangedSegmentsConfirmationRejectedEvent(
            Reservation reservation,
            CanConfirmSegmentsCheck canConfirmSegmentsCheck,
            List<Shared.Models.Segment> segmentsNew)
        {
            var changes = canConfirmSegmentsCheck.Changes.Any(c => c.FlightTimeShiftedTooFar)
                ? ReservationHelper.BuildSegmentsDiffString(reservation, segmentsNew)
                : string.Empty;

            return new ChangedSegmentsConfirmationRejected
            {
                BookingId = reservation.BookingId,
                Locator = LocatorsHelper.GetProviderCode(reservation.Locators),
                PaxLastname = reservation.Passenger.LastName,
                FlightNumberChanged = canConfirmSegmentsCheck.Changes.Any(c => c.FlightNumberChanged),
                FlightTimeShiftedTooFar = canConfirmSegmentsCheck.Changes.Any(c => c.FlightTimeShiftedTooFar),
                HasUnconfirmedSegments = canConfirmSegmentsCheck.HasUnconfirmedSegments,
                Changes = changes
            };
        }

        private List<IMessage> CreateFlightTimeChangedEvents(Reservation reservation, IEnumerable<FlightChange> flightChanges)
        {
            var logger = Logger
                .ForContext("BookingId", reservation.BookingId)
                .ForContext("ReservationId", reservation.Id)
                .ForContext("TenantId", reservation.TenantId);
            
            var messageList = new List<IMessage>();
            foreach (var flightChange in flightChanges)
            {
                var isDepartureTimeChanged = flightChange.DepartureShiftMinutes != decimal.Zero;
                var isValidForEvent = isDepartureTimeChanged
                    && !flightChange.FlightNumberChanged
                    && !flightChange.IsDepartureDateTooFar;

                if (isValidForEvent)
                {
                    logger.ForContext("FlightChange", flightChange, true)
                        .Information("Flight change event is dispatched for {ReservationId}", reservation.Id);

                    messageList.Add(new FlightTimeChanged
                    {
                        ReservationId = reservation.Id,
                        BookingId = reservation.BookingId,
                        FlightNumber = $"{flightChange.Carrier}{flightChange.FlightNumber}",
                        NewDepartureTime = flightChange.Departure,
                        NewDepartureDate = flightChange.DepartureDate,
                        PreviousDepartureDate = flightChange.PreviousDepartureDate,
                        PreviousDepartureTime = flightChange.PreviousDepartureTime
                    });
                }
                else
                {
                    logger.ForContext("FlightChange", flightChange, true)
                        .Information("Flight change event is not valid {ReservationId}", reservation.Id);
                }
            }

            return messageList;
        }
    }
}
