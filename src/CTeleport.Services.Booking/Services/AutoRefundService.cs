using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using CTeleport.Common.Exceptions;
using CTeleport.Common.Extensions;
using CTeleport.Services.Booking.Configuration;
using CTeleport.Services.Booking.Core;
using CTeleport.Services.Booking.Core.Dto;
using CTeleport.Services.Booking.Core.Interfaces;
using CTeleport.Services.Booking.Extensions;
using CTeleport.Services.Booking.Helpers;
using CTeleport.Services.Booking.Helpers.Interfaces;
using CTeleport.Services.Booking.Models;
using CTeleport.Services.Booking.Options;
using CTeleport.Services.Booking.Shared.Constants;
using CTeleport.Services.Booking.Shared.Models;
using CTeleport.Services.FareTerms;
using CTeleport.Services.FareTerms.Shared;
using CTeleport.Services.FareTerms.Shared.Dto;
using CTeleport.Services.FareTerms.Shared.Requests;
using CTeleport.Services.FareTerms.Shared.Responses;
using CTeleport.Services.Helpers;
using CTeleport.Services.Helpers.Extensions;
using CTeleport.Services.Providers.Client;
using CTeleport.Services.Search.Shared.Enums;
using CTeleport.Services.Search.Shared.Models.ProviderTerms;
using Serilog;
using ErrorDescription = CTeleport.Services.Search.Core.Dto.ErrorDescription;

namespace CTeleport.Services.Booking.Services
{
    public class AutoRefundService : IAutoRefundService
    {
        private readonly ILogger _logger;
        private readonly IFareTermsClient _fareTermsService;
        private readonly TicketStateValidationOptions _options;
        private readonly IProvidersClient _providersClient;
        private readonly IProviderTicketStateService _ticketStateService;
        private readonly IRequestAutoRefundBuilder _requestBuilder;
        private readonly RefundOptions _refundOptions;

        public AutoRefundService(ILogger logger,
            IFareTermsClient fareTermsService,
            IProviderTicketStateService ticketStateService,
            TicketStateValidationOptions options,
            IProvidersClient providersClient,
            IRequestAutoRefundBuilder requestBuilder, 
            RefundOptions refundOptions)
        {
            _logger = logger;
            _fareTermsService = fareTermsService;
            _ticketStateService = ticketStateService;
            _options = options;
            _providersClient = providersClient;
            _requestBuilder = requestBuilder;
            _refundOptions = refundOptions;
        }

        public bool CanVoid(Reservation reservation, Ticket ticket)
        {
            // Temporary solution: it's awaiting void support implementation (CT-6110)
            if (!reservation.Source.IsVoidable())
                return false;

            return DateTime.UtcNow < ticket.LastVoidAt;
        }

        public async Task<bool> IsFullyRefundableAsync(Reservation reservation, Ticket ticket)
        {
            return CanVoid(reservation, ticket) || IsTicketFullyRefundable(await CanAutoRefundAsync(reservation, ticket));
        }

        private bool IsTicketFullyRefundable(AutoRefundCheck check)
        {
            return check.CanAutoRefund &&
                   check.FeeAmount == 0 &&
                   check.TaxesToHold.Length == 0;
        }

        public async Task<AutoRefundCheck> CanAutoRefundAsync(Reservation reservation, Ticket ticket, DateTime? refundRequestedAt = null)
        {
            var autoRefund = new AutoRefundErrorHelper(_logger, reservation, ticket);

            // Temporary solution: it's awaiting implementation of provider terms support for FareTerms.OnAutoRefundRequest
            if (!reservation.Source.IsAutoRefundable())
            {
                return autoRefund.IsProhibited(
                    AutoRefundErrorCodes.AutorefundNotSupported, $"Auto-refund is not supported for {reservation.Source.GetProvider()} bookings", false);
            }
            
            // tickets after reissue are not supported in fee calculation and they are prohibited for auto-refund
            if (ticket.ProhibitAutoRefund)
                return autoRefund.IsProhibited(AutoRefundErrorCodes.AutorefundProhibited, "Auto-refund is prohibited.", false);
            
            var promisedTerms = await GetPromisedRefundTermsAsync(reservation, ticket, refundRequestedAt);

            OnAutoRefundResponse refundFeesResponse;
            
            if (reservation.Source.IsRefundFeeCalculatedByProvider())
            {
                refundFeesResponse = await GetRefundFeesFromProviderAsync(reservation, ticket, promisedTerms.Fee);
            }
            else
            {
                var provider = await _providersClient.GetProviderAsync(reservation.Source);
                var refundFeesRequest = _requestBuilder.Build(reservation, ticket, refundRequestedAt, provider.Country);
                
                var getRefundFeesTask = _fareTermsService.GetRefundFeesAsync(refundFeesRequest);
                refundFeesResponse = await getRefundFeesTask;
            }

            var hasIrregularities = reservation.Irregularities != null && (reservation.Irregularities.Rescheduled || reservation.Irregularities.Cancelled);
            
            var isPredicted = refundFeesResponse.IsPredicted;

            if (isPredicted)
            {
                if (GetActualRefundCondition(refundFeesResponse) == RefundCondition.Unknown)
                {
                    return autoRefund.IsProhibited(AutoRefundErrorCodes.AutorefundProhibitedPredicted, "Predicted auto-refund is prohibited when fee is unknown.", true);
                }
                
                if (reservation.Price.TotalPriceToTargetCurrency(CurrencyExchange.DefaultCurrency) >= _refundOptions.PriceLimitToForceManualRefundInEuro)
                {
                    return autoRefund.IsProhibited(AutoRefundErrorCodes.PredictedResultWithBigPrice, "Predicted results with a big price.", true);       
                }
            }

            ErrorDescription wrongPromiseError = GetWrongPromiseError(refundFeesResponse, promisedTerms.Condition);
            if (!hasIrregularities && HasErrors(refundFeesResponse, ErrorCode.farerule_non_refundable, ErrorCode.tkt_expired))
                return autoRefund.IsProhibited(wrongPromiseError ?? refundFeesResponse.Error, isPredicted);

            var getTicketCouponsTask = GetTicketCouponsAsync(reservation, ticket);
            var options = _options.For(reservation);
            var ticketCoupons = await getTicketCouponsTask;

            var ticketIsUnused = ticketCoupons.All(c => c.Status == TicketCouponStatus.OPEN || c.Status == TicketCouponStatus.ARPT || c.Status == TicketCouponStatus.CKIN);
            if (ticketIsUnused && !ticket.NoShow && options.IsDuringCooldownPeriod(refundRequestedAt) && options.IsDuringCooldownPeriod()) // NOTE: refund was requested during cooldown period, AND current timestamp is still during cooldown period
                return autoRefund.IsProhibited(AutoRefundErrorCodes.AutorefundCooldownRequired, "Unverified no-show case. Waiting for {Hours} hours after departure.", options.CooldownHoursAfterDeparture, isPredicted);

            if (ticketCoupons.All(c => c.Status == TicketCouponStatus.USED))
                return autoRefund.IsProhibited(AutoRefundErrorCodes.TicketIsFullyUsed, "Ticket is fully used.", isPredicted);

            if (ticketCoupons.Any(c => c.Status == TicketCouponStatus.USED))
                return autoRefund.IsProhibited(AutoRefundErrorCodes.TicketIsPartiallyUsed, "Ticket is partially used.", isPredicted);

            if (ticketCoupons.Any(c => c.Status == TicketCouponStatus.LFTD))
                return autoRefund.IsProhibited(AutoRefundErrorCodes.TicketIsPartiallyUsed, "Some coupon(s) have status LIFTED.", isPredicted);

            if (ticketCoupons.Any(c => c.Status == TicketCouponStatus.EXCH))
                return autoRefund.IsProhibited(AutoRefundErrorCodes.TicketIsExchanged, "Ticket is exchanged.", isPredicted);

            if (ticketCoupons.Any(c => c.Status == TicketCouponStatus.SUSP || c.Status == TicketCouponStatus.UNVL || c.Status == TicketCouponStatus.CKIN))
                return autoRefund.IsProhibited(AutoRefundErrorCodes.TicketIsBlocked, "Ticket coupons are blocked.", isPredicted);

            if (hasIrregularities && (HasErrors(refundFeesResponse, ErrorCode.farerule_non_refundable) || IsPaidRef(refundFeesResponse)))
                return autoRefund.IsProhibited(AutoRefundErrorCodes.AutorefundBlockedByIrregularity, "Refund is blocked by irregularity", isPredicted);

            if (refundFeesResponse.HasError)
                return autoRefund.IsProhibited(refundFeesResponse.Error, isPredicted);

            if (HasPartialTaxes(refundFeesResponse, out string partialTaxes))
                return autoRefund.IsProhibited(AutoRefundErrorCodes.PartialTaxRefund, "Partial tax return is not supported. Taxes to be refunded partially: {Taxes}", partialTaxes, isPredicted);

            // Validate fare calc required to avoid losing taxes. 
            if (!ticket.IsFareCalcValid())
                return autoRefund.IsProhibited(AutoRefundErrorCodes.InvalidFareCalc, "Fare calc {FareCalc} is invalid. Refund in GDS", ticket.Price.FareCalc, isPredicted);

            if (wrongPromiseError != null)
                return autoRefund.IsProhibited(wrongPromiseError, isPredicted);

            return new AutoRefundCheck
            {
                CanAutoRefund = true,
                FeeAmount = refundFeesResponse.Fee,
                FeeCcy = ticket.Price.Currency,
                TaxesToHold = refundFeesResponse.NonRefTaxes.Keys.ToArray(),
                IsPredicted = isPredicted
            };
        }

        private async Task<IList<TicketCoupon>> GetTicketCouponsAsync(Reservation reservation, Ticket ticket)
        {
            var response = await _ticketStateService.GetTicketInfoAsync(new Core.Dto.ProviderRetrieveTicketRequest
            {
                Locators = reservation.Locators,
                DocumentNumber = ticket.Number,
                Source = reservation.Source
            });

            if (response.HasError)
            {
                throw new ServiceException(response.Error.ErrorCode, response.Error.ErrorMessage);
            }

            return response.Ticket.Coupons;
        }

        private static bool IsPaidRef(OnAutoRefundResponse response) => response.Fee > 0 || response.NonRefTaxes?.Keys.Count > 0;

        private static bool HasErrors(OnAutoRefundResponse response, params string[] errorCodes) => response.HasError && errorCodes.Contains(response.Error.ErrorCode);

        private static bool HasPartialTaxes(OnAutoRefundResponse response, out string partialTaxes)
        {
            partialTaxes = null;
            if (response.NonRefTaxes == null)
                return false;

            var nonZeros = response.NonRefTaxes
                .Where(p => p.Value != 0)
                .Select(p => string.Join(":", p.Key, p.Value))
                .ToList();
            if (nonZeros.Count == 0)
                return false;

            partialTaxes = string.Join(", ", nonZeros);
            return true;
        }

        /// <summary>
        /// Checks if actual conditions are not stricter than promised
        /// </summary>
        /// <returns>Null if situation is OK or error describing the problem</returns>
        private static ErrorDescription GetWrongPromiseError(OnAutoRefundResponse response, RefundCondition promisedCondition)
        {
            RefundCondition actualCondition = GetActualRefundCondition(response);
            if (promisedCondition == RefundCondition.Unknown || actualCondition <= promisedCondition)
                return null;

            return new ErrorDescription
            {
                ErrorCode = AutoRefundErrorCodes.ConditionsAreStricterThanPromised,
                ErrorMessage = $"Refund conditions ({actualCondition}) stricter than promised: {promisedCondition}."
            };
        }

        private static RefundCondition GetActualRefundCondition(OnAutoRefundResponse response)
        {
            if (response.HasError)
                return response.Error.ErrorCode == ErrorCode.farerule_non_refundable ? RefundCondition.NonRefundable : RefundCondition.Unknown;

            return IsPaidRef(response) ? RefundCondition.PaidRefund : RefundCondition.Refundable;
        }
        
        private async Task<(RefundCondition Condition, decimal? Fee)> GetPromisedRefundTermsAsync(Reservation reservation, Ticket ticket, DateTime? refundRequestedAt)
        {
            var promisedTerms = await _fareTermsService.GetFareTermsByTimelineAsync(_requestBuilder.BuildPostBookingRequest(reservation, ticket, refundRequestedAt));

            var cancellationTerms = promisedTerms.Terms?.Cancellations?.Current;
            return (cancellationTerms?.Conditions ?? RefundCondition.Unknown, cancellationTerms?.Fee?.Amount);
        }

        private async Task<OnAutoRefundResponse> GetRefundFeesFromProviderAsync(Reservation reservation, Ticket ticket, decimal? promisedFee)
        {
            if (promisedFee.HasValue) //Promised refund fee over fee calculated by provider
            {
                return AutoRefundResponseBuilder.Build(promisedFee.Value);
            }
            
            _logger.Information("Promised refund is null. Sending request to get refund fee from provider. {TicketNumber} {Locator}.", ticket.Number, reservation.GetProviderCode());
            
            var response = await _ticketStateService.GetTicketRefundInfoAsync(new RefundTicketRequest
            {
                Source = reservation.Source,
                Locators = reservation.Locators,
                Number = ticket.Number,
                CorrelationId = reservation.Id
            });
            
            _logger.Information("Received refund fee from provider. {TicketNumber} {Locator} {Fee}.", 
                ticket.Number, reservation.GetProviderCode(), response.RefundFee);

            return AutoRefundResponseBuilder.BuildFromRefundTicketResponse(response);
        }
       

        struct AutoRefundErrorHelper
        {
            static readonly Regex RegexForFormatItem = new Regex(@"\{.+?\}");

            readonly ILogger _logger;
            readonly Reservation _reservation;
            readonly Ticket _ticket;

            public AutoRefundErrorHelper(ILogger logger, Reservation reservation, Ticket ticket)
            {
                _logger = logger;
                _reservation = reservation;
                _ticket = ticket;
            }

            public AutoRefundCheck IsProhibited(ErrorDescription error, bool isPredicted) => IsProhibited(error.ErrorCode, error.ErrorMessage, isPredicted);

            public AutoRefundCheck IsProhibited(string code, string reason, bool isPredicted)
            {
                _logger.Information("Auto refund is not possible for ticket {TicketNumber} ({Locator}). " + reason, _ticket?.Number, _reservation.GetProviderCode());
                return new AutoRefundCheck { ReasonCode = code, Reason = reason, IsPredicted = isPredicted };
            }

            public AutoRefundCheck IsProhibited<T>(string code, string reason, T parameter, bool isPredicted)
            {
                _logger.Information("Auto refund is not possible for {TicketNumber} ({Locator}). " + reason, _ticket.Number, _reservation.GetProviderCode(), parameter);
                reason = RegexForFormatItem.Replace(reason, "{0}");
                return new AutoRefundCheck { ReasonCode = code, Reason = string.Format(reason, parameter), IsPredicted = isPredicted };
            }
        }
    }
}