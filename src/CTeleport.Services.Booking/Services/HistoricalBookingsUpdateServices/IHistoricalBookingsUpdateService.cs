using System.Threading.Tasks;

namespace CTeleport.Services.Booking.Services.HistoricalBookingsUpdateServices
{
    /// <summary>
    /// This service contains the logic for adding EquipmentCode and TechnicalStops to existing bookings by provider.
    /// </summary>
    public interface IHistoricalBookingsUpdateService
    {
        /// <summary>
        /// Query Bookings by provider code and add EquipmentCode and TechnicalStops in reservation segments where information
        /// is missing.
        /// </summary>
        /// <param name="provider">provider code e.g. "1A"</param>
        /// <param name="saveChanges">if true, the command will save changes, otherwise it wont</param>
        Task AddEquipmentCodeAndTechnicalStopsAsync(string provider, bool saveChanges);
    }   
}