using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using CTeleport.Services.Booking.Core.Dto;
using CTeleport.Services.Booking.Core.Interfaces;
using CTeleport.Services.Booking.Models;
using CTeleport.Services.Booking.Repositories;
using CTeleport.Services.Booking.Shared.Models;
using Serilog;

namespace CTeleport.Services.Booking.Services.HistoricalBookingsUpdateServices
{
    public class HistoricalBookingsUpdateService : IHistoricalBookingsUpdateService
    {
        private readonly IProviderReservationInfoService _providerReservationInfoService;
        private readonly IReservationsRepository _reservationsRepository;
        private readonly ILogger _logger;
        private readonly Guid _correlationId;
        private readonly Dictionary<string, int> _statistics;
        
        public HistoricalBookingsUpdateService(
            IProviderReservationInfoService providerReservationInfoService,
            IReservationsRepository reservationsRepository,
            ILogger logger)
        {
            _providerReservationInfoService = providerReservationInfoService;
            _reservationsRepository = reservationsRepository;
            _logger = logger;

            _correlationId = Guid.NewGuid();
            _statistics = new Dictionary<string, int>
            {
                { "UpdatedReservations", 0 },
                { "NullResponse", 0 },
                { "NullFlightReservation", 0 },
                { "HasError", 0 },
                { "NullSegments", 0 },
                { "NullProviderSegments", 0 },
                { "SegmentCountIsDifferent", 0 },
            };
        }

        public async Task AddEquipmentCodeAndTechnicalStopsAsync(string provider, bool saveChanges = false)
        {
            if (!IsValidProvider(provider))
                throw new ArgumentException($"[{nameof(HistoricalBookingsUpdateService)}] Invalid Provider: {provider}");
            
            await ProcessBookingsByProviderAsync(provider, saveChanges);
        }
        
        private async Task ProcessBookingsByProviderAsync(string provider, bool saveChanges)
        {
            var now = DateTime.UtcNow;
            var elapsedTime = Stopwatch.StartNew();
            
            _logger.Information("Starting adding EquipmentCode and TechnicalStops for {Provider} reservations starting from {now} (id: {correlationId})",
                provider, now, _correlationId);
            
            var activeReservations = await _reservationsRepository.GetAllActiveByProviderAsync(provider);
            var reservations = activeReservations
                .Where(r => r.LegSegments != null)
                .Where(r => r.LegSegments.Any(l => l.Any(s => s.EquipmentCode is null)))
                .ToArray();
            
            _logger.Information("Processing {number} reservations (id: {correlationId})", reservations.Length, _correlationId);
            
            foreach (var reservation in reservations)
            {
                var providerReservation = await _providerReservationInfoService.GetReservationAsync(new ProviderRetrieveReservationRequest 
                {
                    ReservationId = reservation.Id,
                    Locators = reservation.Locators,
                    Source = reservation.Source,
                    UseCache = false
                });

                if (providerReservation == null)
                {
                    _logger.Information("Response for Reservation {id} is null (id: {correlationId})", reservation.Id, _correlationId);
                    _statistics["NullResponse"]++;
                    continue;
                }
                
                if (providerReservation.HasError)
                {
                    _logger.Information("Reservation {id} has errors. ErrorCode: {error}, ErrorMessage: {errorMessage} (id: {correlationId})", 
                        reservation.Id, providerReservation.Error?.ErrorCode ?? string.Empty, providerReservation.Error?.ErrorMessage ?? string.Empty, _correlationId);
                    _statistics["HasError"]++;
                    continue;
                }
                
                if (providerReservation.FlightReservation == null)
                {
                    _logger.Information("Provider FlightReservation {id} is null (id: {correlationId})", reservation.Id, _correlationId);
                    _statistics["NullFlightReservation"]++;
                    continue;
                }

                await ReplaceEquipmentCodeAdTechStopsAsync(reservation, providerReservation.FlightReservation,
                    saveChanges);
            }
            
            elapsedTime.Stop();
            _logger.Information("Completed. EquipmentCode and TechnicalStops added for {Provider} reservations starting from {now}. {NumberOfReservations} reservations were processed and {UpdatedReservations} updated. Elapsed time: {0:dd\\.hh\\:mm\\:ss} (id: {correlationId})",
                provider, now, reservations.Length, _statistics["UpdatedReservations"], elapsedTime.Elapsed, _correlationId);
            
            _logger.Information("Null provider responses: {NullResponse}, Response FlightReservation is null: {NullFlightReservation}, Local Number of segments is different from Provider response: {SegmentCountIsDifferent}, Provider response has error: {HasError}, Local Reservation has Null Segments: {NullSegments}, Provider Reservation has Null Segments: {NullProviderSegments} (id: {correlationId})", 
                _statistics["NullResponse"], _statistics["NullFlightReservation"], _statistics["SegmentCountIsDifferent"], _statistics["HasError"], _statistics["NullSegments"], _statistics["NullProviderSegments"], _correlationId);
        }

        private async Task ReplaceEquipmentCodeAdTechStopsAsync(Reservation reservation, ProviderFlightReservation providerReservation, bool saveChanges)
        {
            // We need to merge segments from all legs into one list.
            // the order of the segments are the same as the providerReservation segment's list.
            var segments = reservation.LegSegments?.SelectMany(i => i).ToList();
            var providerSegments = providerReservation.Segments;

            if (segments == null)
            {
                _logger.Information("Segments are null. Reservation Id: {ReservationId} (id: {correlationId})", reservation.Id, _correlationId);
                _statistics["NullSegments"]++;
                return;
            }
            
            if (providerSegments == null)
            {
                _logger.Information("Provider Segments are null. Reservation Id: {ReservationId} (id: {correlationId})", reservation.Id, _correlationId);
                _statistics["NullProviderSegments"]++;
                return;
            }
                
            if (segments.Count != providerSegments.Count)
            {
                _logger.Information(
                    "Segment count ({SegmentCount}) is different from providers count ({ProviderSegmentCount}). Reservation Id: {ReservationId} (id: {correlationId})", 
                    segments.Count, providerSegments.Count, reservation.Id, _correlationId);
                _statistics["SegmentCountIsDifferent"]++;
                return;
            }

            bool isUpdated = false;
            
            // Replace EquipmentCode and TechnicalStops
            for (var i = 0; i < segments.Count; i++)
            {
                if (providerSegments[i].EquipmentCode != null)
                {
                    segments[i].EquipmentCode = providerSegments[i].EquipmentCode;
                    isUpdated = true;
                }

                if (providerSegments[i].TechnicalStops != null)
                {
                    segments[i].TechnicalStops = providerSegments[i].TechnicalStops;
                    isUpdated = true;
                }
            }

            // We have the option to test number of items to be processed if saveChanges is false
            if (saveChanges && isUpdated)
            {
                await _reservationsRepository.UpdateSegmentsAsync(reservation);
                _statistics["UpdatedReservations"]++;
            }
                
            _logger.Information("{testFlag} Reservation Id {id}, Segments: {NumberSegments}, Provider Segments: {NumberProviderSegments}, was updated? {isUpdated} (id: {correlationId})", 
                saveChanges ? string.Empty : "(Testing)", reservation.Id, segments.Count, providerSegments.Count, isUpdated, _correlationId);
        }
        
        private static bool IsValidProvider(string provider)
            => provider is CTeleport.Services.Helpers.Constants.Providers.AMADEUS 
                or CTeleport.Services.Helpers.Constants.Providers.GALILEO 
                or CTeleport.Services.Helpers.Constants.Providers.TRAVELFUSION;
    }
}