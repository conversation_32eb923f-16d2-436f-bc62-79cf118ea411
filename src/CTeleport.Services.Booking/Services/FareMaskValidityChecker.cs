using System;
using System.Linq;
using System.Threading.Tasks;
using CTeleport.Services.Booking.Configuration;
using CTeleport.Services.Booking.Core.Dto;
using CTeleport.Services.Booking.Core.Interfaces;
using CTeleport.Services.Booking.Models;
using CTeleport.Services.Booking.Services.Interfaces;
using CTeleport.Services.Helpers;
using CTeleport.Services.VoidCalc.Interfaces;
using CTeleport.Services.VoidCalc.Services;
using Serilog;

namespace CTeleport.Services.Booking.Services;

public sealed class FareMaskValidityChecker : IFareMaskValidityChecker
{
    private readonly IPccDateTimeConverter _dateTimeConverter;
    private readonly IProviderReservationInfoService _providerReservationInfoService;
    private readonly ILogger _logger;
    private readonly TicketingOptions _options; // TODO: Better IOptions<TicketingOptions>

    public FareMaskValidityChecker(IProviderReservationInfoService providerReservationInfoService, 
        IPccDateTimeConverter dateTimeConverter, TicketingOptions options, ILogger logger)
    {
        _providerReservationInfoService = providerReservationInfoService;
        _dateTimeConverter = dateTimeConverter;
        _options = options;
        _logger = logger;
    }
        
    public async ValueTask<bool> FareMaskRefreshNeeded(Reservation reservation)
    {
        if (_options?.FareMaskValidity?.RefreshRequired == null)
        {
            _logger.Information("Options not set");
            return false;
        }
        
        var provider = reservation.Source.GetProvider();
        
        var hasProvider = _options.FareMaskValidity.RefreshRequired.TryGetValue(provider, out var carriers);
        var hasCarrier = carriers?.Contains(reservation.Fare.PlatingCarrier) ?? false;
        
        if (hasProvider == false || hasCarrier == false)
        {
            _logger.Information("No fare mask manipulation needed");
            return false;
        }

        _logger.Information("Fare mask must to be checked");
            
        var request = new ProviderRetrieveReservationRequest
        {
            Locators = reservation.Locators,
            Source = reservation.Source
        };
                        
        var providerReservation = await _providerReservationInfoService.GetReservationAsync(request);
                        
        var fareQuotedOnDate = providerReservation.FlightReservation.Fare.QuotedOn;
        var pccDateTime = _dateTimeConverter.GetDateTimeAtPCC(reservation.Source);
        var pccToday = DateOnly.FromDateTime(pccDateTime);
        
        if (fareQuotedOnDate is null || pccToday == fareQuotedOnDate)
        {
            _logger.Information("TST should be valid, because {Source} time {SourceTime} and fare mask quoted on {FareMaskQuotedOn}", reservation.Source, pccDateTime, fareQuotedOnDate);
            return false;
        }
            
        _logger.Information("Fare must be refreshed, because {Source} time {SourceTime} and fare mask quoted on {FareMaskQuotedOn}", reservation.Source, pccDateTime, fareQuotedOnDate);

        return true;
    }
}