using System.Threading.Tasks;
using CTeleport.Services.Booking.Models;

namespace CTeleport.Services.Booking.Services.Interfaces;

public interface IFareMaskValidityChecker
{
    /// <summary>
    /// Checks if fare refresh need to be call due to outdated fare mask
    /// </summary>
    /// <param name="reservation">Reservation model in our system</param>
    /// <returns></returns>
    ValueTask<bool> FareMaskRefreshNeeded(Reservation reservation);
}