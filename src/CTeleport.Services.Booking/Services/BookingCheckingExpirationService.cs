using AutoMapper;
using CTeleport.Common.Messaging.Services;
using CTeleport.Messages.Events.Bookings;
using CTeleport.Services.Booking.Configuration;
using CTeleport.Services.Booking.Repositories;
using SnowflakeGenerator;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using CommonBooking = CTeleport.Messages.Commands.Bookings.CommonObjects.Booking.Booking;
using CommonReservation = CTeleport.Messages.Commands.Bookings.CommonObjects.Reservation.Reservation;

namespace CTeleport.Services.Booking.Services;

internal class BookingCheckingExpirationService : IBookingCheckingExpirationService
{
    private readonly IBookingsRepository _bookingsRepository;
    private readonly IReservationsRepository _reservationsRepository;
    private readonly BookingSoonExpirationOptions _options;
    private readonly IMessageDispatcher _dispatcher;
    private readonly IMapper _mapper;

    private static readonly Snowflake _snowflake = new(new SnowflakeGenerator.Settings
    {
        MachineID = 1,
        CustomEpoch = new DateTimeOffset(2020, 1, 1, 0, 0, 0, TimeSpan.Zero)
    });

    public BookingCheckingExpirationService(
        IBookingsRepository bookingsRepository,
        IReservationsRepository reservationsRepository,
        BookingSoonExpirationOptions options,
        IMessageDispatcher dispatcher,
        IMapper mapper)
    {
        _bookingsRepository = bookingsRepository;
        _reservationsRepository = reservationsRepository;
        _options = options;
        _dispatcher = dispatcher;
        _mapper = mapper;
    }

    public async Task DispatchBookingsWithSoonExpirationAsync(CancellationToken cancellationToken)
    {
        var startDateForBookings = DateTime.UtcNow.Subtract(TimeSpan.FromDays(_options.TakeBookingsForLastDays));
        var endDateForBookings = DateTime.UtcNow;

        const int batchSize = 10;
        var bookingsBatches = _bookingsRepository.GetRefundubleBookingsByBatchesAsync(
            startDateForBookings, endDateForBookings, batchSize, cancellationToken);

        var startDateForReservations = DateTime.UtcNow.Add(TimeSpan.FromHours(_options.StartWindowForReservationsInHours));
        var endDateForReservations = DateTime.UtcNow.Add(TimeSpan.FromHours(_options.EndWindowForReservationsInHours));

        await foreach (var bookingsBatch in bookingsBatches)
        {
            var bookingsIds = bookingsBatch.Select(b => b.Id).ToArray();

            var reservations = await _reservationsRepository.GetReservationsWithSoonExpiration(
                bookingsIds, startDateForReservations, endDateForReservations, cancellationToken);
            
            foreach (var bookingReservations in reservations.GroupBy(r => r.BookingId))
            {
                var bookingId = bookingReservations.Key;

                var booking = bookingsBatch.First(b => b.Id == bookingId);

                var mappedBooking = _mapper.Map<CommonBooking>(booking);
                var mappedReservations = _mapper.Map<IReadOnlyCollection<CommonReservation>>(bookingReservations);

                await _dispatcher.DispatchAsync(new BookingHasExpirationCancellation
                {
                    MessageId = _snowflake.NextID(),
                    Booking = mappedBooking,
                    Reservations = mappedReservations
                });
            }
        }
    }
}
