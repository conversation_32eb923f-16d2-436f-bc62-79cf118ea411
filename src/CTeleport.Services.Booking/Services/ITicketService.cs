using System;
using System.Threading.Tasks;
using CTeleport.Services.Search.Shared.Models;

namespace CTeleport.Services.Booking.Services
{
    public interface ITicketService
    {
        /// <summary>
        /// Prevent ticket issue near midnight
        /// </summary>
        /// <param name="source"></param>
        /// <returns></returns>
        bool CanIssueTicketNow(string source);

        /// <summary>
        /// Method calculates ticketing time based on the features of current fare stored for reservation,
        /// on latestTicketingTime and fareGuaranteeDate indicated by UAPI, date and time of scheduled departure
        /// and date and time when booking was created.
        /// It also applies custom TTL for selected plating carriers.
        /// </summary>
        /// <param name="reservation">Reservation</param>
        /// <param name="clientPaymentExpirationDate">Last date we can collect money from client's card</param>
        /// <param name="easyPayExpirationDate">Expiration year-month of the EasyPay card used to purchase the ticket</param>
        /// <returns>Date and time for auto ticketing</returns>
        DateTime CalculateTicketingTime(Models.Reservation reservation, DateTime? clientPaymentExpirationDate, DateTime? easyPayExpirationDate = null);

        /// <summary>
        /// Method calculates last void time of reservation tickets
        /// </summary>
        /// <param name="reservation">The reservation</param>
        /// <param name="issueAt">Ticket issue date and time, UTC</param>
        /// <returns>Latest date and time when issued ticket can be voided</returns>
        DateTime CalcLastVoidTimeForReservation(Models.Reservation reservation, DateTime issueAt);

        /// <summary>
        /// Translates fare guarantee date (at source TZ) to ultimate date and time of expiration, UTC 
        /// In the case when fare guarantee date is empty or null, a fallback date is used: Current Date in source's time zone. In such case,
        /// the Expiration Date and time retrieved will be set to 23:59 of the current day in source's time zone. (in UTC)
        /// </summary>
        /// <param name="source">The id of a source, e.g. "1G.8WG0"</param>
        /// <param name="fareGuaranteeDate">The date till which the fare is guaranteed in Galileo, in yyyy-MM-dd format</param>
        /// <returns>Ultimate expiration date and time expiration, UTC</returns>
        DateTime GetExpirationDateTime(string source, string fareGuaranteeDate);

        /// <summary>
        /// Checks if the current fare stored for reservation expires today (at PCC timezone)
        /// </summary>
        /// <param name="reservation">Reservation</param>
        /// <returns>True if fare expires today</returns>
        bool DoesFareExpireToday(Models.Reservation reservation);

        /// <summary>
        /// Returns current date at the specified source presented as a string in yyyy-MM-dd format
        /// </summary>
        /// <param name="source">The id of a source, e.g. "1G.8WG0"</param>
        /// <param name="value">DateTime value to convert to PCC date, current moment is taken if argument value is null</param>
        /// <returns>Date value at PCC</returns>
        string GetDateAtPCC(string source, DateTime? datetime = null);

        /// <summary>
        /// Determines immediate ticketing for solution
        /// </summary>
        /// <param name="flightSolution"></param>
        /// <param name="isApprovalRequired"></param>
        /// <param name="providerKey"></param>
        /// <returns></returns>
        Task<bool> IsImmediateTicketing(FlightSolution flightSolution, bool isApprovalRequired, string providerKey);
    }
}
