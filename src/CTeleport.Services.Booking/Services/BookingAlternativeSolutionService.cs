using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using CTeleport.Common.Exceptions;
using CTeleport.Services.Booking.Enums;
using CTeleport.Services.Search.Shared.Models;
using CTeleport.Services.SearchProxy.Services;

namespace CTeleport.Services.Booking.Services
{
    public class BookingAlternativeSolutionService : IBookingAlternativeSolutionService
    {
        private readonly IFlightSolutionService _flightSolutionService;
        private readonly IBookingService _bookingService;
        private readonly IBookingSagaService _bookingSagaService;
        private readonly IMapper _mapper;

        public BookingAlternativeSolutionService(IFlightSolutionService flightSolutionService, IBookingService bookingService,
            IBookingSagaService bookingSagaService, IMapper mapper)
        {
            _flightSolutionService = flightSolutionService;
            _bookingService = bookingService;
            _bookingSagaService = bookingSagaService;
            _mapper = mapper;
        }

        public async Task<IEnumerable<FlightSolutionDetails>> GetFlightSolutionAlternativeAsync(
            string searchAuthToken,
            string flightSolutionId,
            string departDate,
            string bookingId = null)
        {
            var sourceSolution = await _flightSolutionService.GetFlightSolutionAsync(flightSolutionId);
            if (sourceSolution != null)
            {
                return await _flightSolutionService.GetFlightSolutionAlternativeAsync(searchAuthToken, sourceSolution, departDate);
            }

            // Try to find booking with flight solution or by specified id:
            var booking = string.IsNullOrEmpty(bookingId)
                ? await _bookingService.GetBookingByFlightSolutionAsync(flightSolutionId)
                : await _bookingService.GetBookingAsync(bookingId);

            if (booking is null)
            {
                throw new NotFoundException("Nor flight solution or booking is found");
            }

            var reservations = await _bookingService.GetBookingReservationsAsync(booking.Id);
            var activeReservations = reservations.Where(r => r.State == ReservationState.Active).ToList();
            if (activeReservations.Count == 0)
            {
                throw new InvalidOperationException("Flight solution's booking has no active reservations");
            }

            var bookingSaga = await _bookingSagaService.GetAsync(bookingId);
            var providerAlternativeRequests = _mapper.Map<IList<AlternativeFlightSolutionsRequest>>(activeReservations);
            var priceOriginalCurrency = activeReservations.First().Price.OriginalCurrency;
            var solution = new FlightSolution
            {
                LegSegments = _mapper.Map<FlightSolution>(bookingSaga).LegSegments,
                SearchId = booking.SearchId,
                RouteId = booking.RouteId,
                SearchJobId = activeReservations.First().SearchJobMetadata?.SearchJobId,
                Price = new FlightSolutionPrice
                {
                    OriginalCcy = priceOriginalCurrency,
                    Ccy = booking.Price.Currency
                },
                Terms = new FlightSolutionTerms
                {
                    Splitting = booking.Terms.Splitting
                }
            };

            var searchContext = new SearchContext
            {
                NeedTravelPolicy = true,
                Tenant = booking.TenantId,
                VesselName = booking.Metadata.VesselName,
                OriginalCcy = priceOriginalCurrency,
                Ccy = booking.Price.Currency,
                Source = providerAlternativeRequests.First().Source
            };

            var result = await _flightSolutionService.RunFlightSolutionAlternativeAsync(searchAuthToken, providerAlternativeRequests, solution, searchContext);
            return result;
        }
    }
}
