using System.Threading.Tasks;
using CTeleport.Services.Search.Shared.Models;
using BookingSaga = CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Booking;

namespace CTeleport.Services.Booking.Services;

/// <summary>
/// Provides a simulated implementation of incoming operations within the booking process, used as part of the testing
/// of a new booking engine. This class isolates the booking process for testing purposes without making real bookings
/// or affecting the live system.
/// </summary>
public interface IBookingVariantService
{
    /// <summary>
    /// Simulates the start of a booking process
    /// </summary>
    /// <remarks>
    /// This is part of the testing of a new booking engine where the behavior of starting
    /// a booking is isolated for testing purposes without actually affecting the real system.
    /// </remarks>
    Task StartBooking(BookingSaga saga, FlightSolution flightSolution);

    /// <summary>
    /// Simulates the approval of a booking
    /// </summary>
    /// <remarks>
    /// This is part of the testing of a new booking engine where the behavior of starting
    /// a booking is isolated for testing purposes without actually affecting the real system.
    /// </remarks>
    Task ApproveBooking(string id, CTeleport.Messages.Commands.Models.User user);

    /// <summary>
    /// Simulates the rejection of a booking
    /// </summary>
    /// <remarks>
    /// This is part of the testing of a new booking engine where the behavior of starting
    /// a booking is isolated for testing purposes without actually affecting the real system.
    /// </remarks>
    Task RejectBooking(string id, string reason);
}