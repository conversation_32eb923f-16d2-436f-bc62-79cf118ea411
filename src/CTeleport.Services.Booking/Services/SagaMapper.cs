using CTeleport.Messages.Models;
using CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using BookingSaga = CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Booking;
using CommonBooking = CTeleport.Messages.Commands.Bookings.CommonObjects.Booking.Booking;
using CommonReservation = CTeleport.Messages.Commands.Bookings.CommonObjects.Reservation.Reservation;

namespace CTeleport.Services.Booking.Services;

public class SagaMapper : ISagaMapper
{
    public CommonBooking MapToCommonBooking(BookingSaga saga)
    {
        var booking = new CommonBooking();

        booking.Id = saga.Id;
        booking.RouteId = saga.FlightSolutionEntity?.RouteId;

        booking.Passenger = new Messages.Commands.Models.PassengerDetails
        {
            Id = null, // no info
            FirstName = saga.FlightSolutionEntity?.Passenger?.FirstName,
            LastName = saga.FlightSolutionEntity?.Passenger?.LastName,
            DateOfBirth = saga.FlightSolutionEntity?.Passenger?.DateOfBirth,
            SingleNameOnly = saga.FlightSolutionEntity?.Passenger?.SingleNameOnly ?? false,
            Gender = saga.FlightSolutionEntity?.Passenger.Gender == Domain.Aggregates.BookingAggregate.Enums.Gender.Male
                ? "M"
                : saga.FlightSolutionEntity?.Passenger.Gender == Domain.Aggregates.BookingAggregate.Enums.Gender.Female
                ? "F"
                : null,
            Nationality = saga.FlightSolutionEntity?.Passenger?.Nationality,
            DocNumber = saga.FlightSolutionEntity?.Passenger?.DocumentNumber,
            DocCountry = saga.FlightSolutionEntity?.Passenger?.DocumentCountry,
            DocExpire = saga.FlightSolutionEntity?.Passenger?.DocumentExpire,
            DocType = saga.FlightSolutionEntity?.Passenger?.DocumentType == Domain.Aggregates.BookingAggregate.Enums.DocumentType.Passport
                ? "Passport"
                : saga.FlightSolutionEntity?.Passenger?.DocumentType == Domain.Aggregates.BookingAggregate.Enums.DocumentType.Unknown
                ? "Unknown"
                : null,
            Email = saga.FlightSolutionEntity?.Passenger?.Email
        };

        booking.Terms = new Messages.Commands.Bookings.CommonObjects.Booking.BookingTerms
        {
            CanCancel = saga.BookingEntity?.Terms?.CanCancel ?? false,
            FareType = saga.BookingEntity?.Terms?.FareType.Name,
            Changes = saga.BookingEntity?.Terms?.Changes?.Name,
            Cancellations = saga.BookingEntity?.Terms?.Cancellations?.Name,
            Splitting = false // no information
        };

        booking.Metadata = new Messages.Commands.Bookings.CommonObjects.Booking.BookingMetadata
        {
            CrewChangeAirport = saga.FlightSolutionEntity?.CrewChangeAirport,
            CrewChangeDate = saga.FlightSolutionEntity?.CrewChangeDate,
            CrewChangeMember = saga.FlightSolutionEntity?.CrewChangeMember,
            VesselFlag = saga.FlightSolutionEntity?.VesselFlag,
            VesselName = saga.FlightSolutionEntity?.VesselName,
            CustomFields = saga.FlightSolutionEntity?.CustomFields.ToDictionary(kv => kv.Key, kv => kv.Value)
        };

        booking.State = saga.BookingEntity?.State switch
        {
            Domain.Aggregates.BookingAggregate.Enums.BookingState.ApprovalRequired => Messages.Commands.Enums.BookingState.ApprovalRequired,
            Domain.Aggregates.BookingAggregate.Enums.BookingState.Confirmed => Messages.Commands.Enums.BookingState.Confirmed,
            Domain.Aggregates.BookingAggregate.Enums.BookingState.Cancelled => Messages.Commands.Enums.BookingState.Cancelled,
            _ => Messages.Commands.Enums.BookingState.Declined
        };

        booking.Price = new Messages.Commands.Bookings.CommonObjects.Booking.BookingPrice
        {
            Currency = saga.BookingEntity?.Price?.Currency,
            Kickback = saga.BookingEntity?.Price?.Kickback ?? 0,
            Markup = saga.BookingEntity?.Price?.Markup ?? 0,
            PerMile = saga.BookingEntity?.Price?.PerMile ?? 0,
            Total = saga.BookingEntity?.Price?.Total ?? 0,
        };

        booking.DepartureAt = new DateTime(saga?.FlightSolutionEntity?.Itinerary?.DepartureTimestampUtc ?? 0);

        booking.CreatedAt = new DateTime(saga.CreatedAtTimestamp);

        booking.UpdatedAt = new DateTime(saga.UpdatedAtTimestamp);

        booking.CreatedBy = new Messages.Commands.Models.User
        {
            Id = saga.BookingEntity?.CreatedBy?.UserIdentity,
            Email = saga.BookingEntity?.CreatedBy?.Email,
            Name = saga.BookingEntity?.CreatedBy?.Name,
            TenantId = null // no info
        };

        booking.TenantId = saga.BookingEntity?.TenantId;

        booking.Comment = saga.BookingEntity?.Comment;

        booking.ApprovedBy = saga.BookingEntity?.ApprovedBy.Select(x => new CTeleport.Messages.Commands.Models.User
        {
            Id = x?.UserIdentity,
            Email = x?.Email,
            Name = x?.Name
        }).ToArray();

        booking.PaymentMethod = saga.BillingEntity?.PaymentMethod switch
        {
            Domain.Aggregates.BookingAggregate.Enums.PaymentMethod.CreditCard => Messages.Commands.Enums.PaymentMethodType.CreditCard,
            Domain.Aggregates.BookingAggregate.Enums.PaymentMethod.AmexBta => Messages.Commands.Enums.PaymentMethodType.AmexBta,
            Domain.Aggregates.BookingAggregate.Enums.PaymentMethod.BankTransfer => Messages.Commands.Enums.PaymentMethodType.BankTransfer,
            _ => Messages.Commands.Enums.PaymentMethodType.VirtualBalance
        };

        booking.PaymentId = saga.BillingEntity?.PaymentId;

        booking.PaymentExpiresAt = saga.BillingEntity?.PaymentExpiry;

        booking.InvoiceeId = saga.BillingEntity?.InvoiceeId ?? 0;

        booking.FrequentFlyerNumbers = saga.BookingEntity?.FreqFlyerNums.ToDictionary(kv => kv?.Number, kv => new Messages.Commands.Models.FrequentFlyerNumber
        {
            Carrier = kv?.Carrier,
            Code = kv?.Code,
            Number = kv?.Number
        });

        return booking;
    }

    public IReadOnlyCollection<CommonReservation> MapToCommonReservations(BookingSaga saga)
    {
        return saga.Reservations.Select(Map).ToArray();

        CommonReservation Map(ReservationEntity reservation)
        {
            return new CommonReservation
            {
                Id = reservation.Id,
                BookingId = saga.Id,
                State = reservation.State.ToString(),
                Legs = reservation.ProviderItinerary?.Legs?.Select(leg => new Messages.Commands.Models.Leg
                {
                    Destination = reservation.ProviderItinerary?.Destination,
                    Origin = reservation.ProviderItinerary?.Origin,
                    ArrivalUtc = (int)(reservation.ProviderItinerary?.ArrivalTimestampUtc ?? 0),
                    DepartureUtc = (int)(reservation.ProviderItinerary?.DepartureTimestampUtc ?? 0)
                }).ToArray(),
                LegSegments = reservation.ProviderItinerary?.Legs?.Select(leg => leg.Segments.Select(segment => new Messages.Commands.Models.Segment
                {
                    ArrivalDate = segment.ArrivalDate,
                    DepartureDate = segment.DepartureDate,
                    Origin = segment.Origin,
                    Destination = segment.Destination,
                    Carrier = segment.Carrier,
                    BookingCode = segment.BookingCode,
                    FareBasis = segment.FareBasis,
                    FlightNumber = segment.FlightNumber,
                    ArrivalTime = DateTimeOffset.FromUnixTimeSeconds(segment.ArrivalTimestamp).DateTime.ToString("HH:mm"),
                    DepartureTime = DateTimeOffset.FromUnixTimeSeconds(segment.DepartureTimestamp).DateTime.ToString("HH:mm"),
                }).ToArray()).ToArray()
            };
        }
    }
}
