using CTeleport.Infrastructure.Metrics;
using CTeleport.Services.Booking.Constants;
using CTeleport.Services.Booking.Services.Interfaces;
using System.Collections.Generic;

namespace CTeleport.Services.Booking.Services
{
    public class BookingMetricsService : IBookingMetricsService
    {
        private readonly IMetric _metric;

        private const string TenantIdTagName = "tenant_id";
        private const string ErrorCodeTagName = "error_code";
        private const string ProviderSourceTagName = "provider_source";
        private const string HasErrorTagName = "has_error";

        public BookingMetricsService(IMetric metric)
        {
            _metric = metric;
        }

        public void IncrementActiveBookingSagaCounter(string tenantId)
            => _metric.Increment(TelemetryCounterNames.ActiveBookingSagaCounter,
                new KeyValuePair<string, object>[] { new(TenantIdTagName, tenantId) });

        public void DecrementActiveBookingSagaCounter(string tenantId)
            => _metric.Decrement(TelemetryCounterNames.ActiveBookingSagaCounter,
                new KeyValuePair<string, object>[] { new(TenantIdTagName, tenantId) });

        public void IncrementBookingSagaRejectedCounter(string tenantId, string errorCode)
            => _metric.Increment(TelemetryCounterNames.BookingSagaRejectedCounter,
                new(TenantIdTagName, tenantId),
                new(ErrorCodeTagName, errorCode));

        public void IncrementBookingCreatedCounter(string tenantId)
            => _metric.Increment(TelemetryCounterNames.BookingCreatedCounter,
                new KeyValuePair<string, object>[] { new(TenantIdTagName, tenantId) });

        public void IncrementProviderValidationRequestCounter(string tenantId, string source, bool hasError)
            => _metric.Increment(TelemetryCounterNames.ProviderValidationRequestCounter,
                new(TenantIdTagName, tenantId),
                new(ProviderSourceTagName, source),
                new(HasErrorTagName, hasError));
    }
}
