using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using CTeleport.Services.Booking.Dto;
using CTeleport.Services.Booking.Models;

namespace CTeleport.Services.Booking.Services
{
    public interface ISingleNameService
    {
        /// <summary>
        /// Set single name according to airline setting
        /// </summary>
        /// <param name="passenger"></param>
        /// <param name="carrier">Plating carrier, eg. KL</param>
        /// <returns></returns>
        Task SetSingleNameAsync(PassengerDetails passenger, string carrier);

        /// <summary>
        /// Returns true if setting exists for specified plating carrier
        /// </summary>
        /// <param name="carrier">Plating carrier</param>
        /// <returns></returns>
        Task<bool> SettingExistsAsync(string carrier);

        /// <summary>
        /// Gets all settings
        /// </summary>
        /// <param name="cancellationToken"></param>
        Task<IReadOnlyCollection<SingleNameSetting>> GetAllAsync(CancellationToken cancellationToken);
        
        /// <summary>
        /// Gets settings for carrier
        /// </summary>
        Task DeleteByCarrierAsync(string carrier);

        /// <summary>
        /// Creates or updates settings for carrier
        /// </summary>
        Task<SingleNameSettingResponse> CreateOrUpdateAsync(string carrier, string pattern);
    }
}