using System;
using System.Linq;
using System.Threading.Tasks;
using CTeleport.Common.Exceptions;
using CTeleport.Common.Messaging.Services;
using CTeleport.Messages.Commands.Bookings;
using CTeleport.Messages.Commands.Reservations;
using CTeleport.Messages.Events.Reservations;
using CTeleport.Services.Booking.Enums;
using CTeleport.Services.Booking.Services.Interfaces;
using CTeleport.Services.Helpers;
using CTeleport.Services.Helpers.Extensions;

namespace CTeleport.Services.Booking.Services;

public class ReservationManualCancellationService : IReservationManualCancellationService
{
    private readonly IBookingService _bookingService;
    private readonly IBookingSagaService _bookingSagaService;
    private readonly IMessageDispatcher _messageDispatcher;
    private readonly IAuthService _authService;

    public ReservationManualCancellationService(
        IBookingService bookingService,
        IBookingSagaService bookingSagaService,
        IMessageDispatcher messageDispatcher,
        IAuthService authService)
    {
        _bookingService = bookingService;
        _bookingSagaService = bookingSagaService;
        _messageDispatcher = messageDispatcher;
        _authService = authService;
    }

    /// <summary>
    /// This method is used to manually cancel a reservation without cancellation on provider side.
    /// Most of the code is taken from <see cref="CTeleport.Services.Booking.Handlers.ProviderCancelledReservationHandler"/>
    /// </summary>
    /// <param name="command">Command</param>
    public async Task ManualCancelReservationAsync(SetReservationAsCancelled command)
    {
        var reservation = await _bookingService.GetReservationAsync(command.ReservationId);
        if (!reservation.Source.CanBeMarkedAsCancelled())
        {
            throw new ValidationException($"Cannot manual cancel non-{reservation.Source.GetProvider()} reservation");
        }

        if (reservation.IsVirtual)
        {
            throw new ValidationException("Cannot set virtual reservation as Cancelled. Use CancelReservation command");
        }

        if (reservation.State != ReservationState.Active)
        {
            throw new ValidationException("Cannot set non-active reservation as cancelled");
        }

        var booking = await _bookingService.GetBookingAsync(reservation.BookingId);
        await _authService.ValidateIfBookingCanBeManagedOrFailAsync(booking);

        var saga = await _bookingSagaService.GetAsync(booking.Id);
        var sagaAlreadyCancelled = saga?.Reservations.Single(x => x.Id == reservation.Id)
            .State == Domain.Aggregates.BookingAggregate.Enums.ReservationState.Cancelled;
        if (saga is not null && !sagaAlreadyCancelled)
        {
            saga = await _bookingSagaService.CancelReservationAsync(saga.Id, reservation.Id);
        }

        await _bookingService.ChangeReservationStateAsync(reservation.Id, ReservationState.Cancelled);

        await _messageDispatcher.DispatchAsync(new ReservationCancelled
        {
            Source = reservation.Source,
            CancelledAt = command.Request?.CreatedAt ?? DateTime.UtcNow,
            TenantId = reservation.TenantId,
            Locator = LocatorsHelper.GetProviderCode(reservation.Locators),
            ReservationId = reservation.Id,
            BookingId = reservation.BookingId,
            CancelledBy = command.User?.Email ?? command.User?.Name,
            ManualCancellation = true
        });
        
        await _messageDispatcher.DispatchAsync(new ResetBookingState
        {
            Request = command.Request,
            BookingId = reservation.BookingId,
            User = command.User
        });
    }
}