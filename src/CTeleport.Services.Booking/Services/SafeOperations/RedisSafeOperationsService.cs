using ServiceStack.Redis;
using System;

namespace CTeleport.Services.Booking.Services.SafeOperations
{
    public class RedisSafeOperationsService : IRedisSafeOperationsService
    {
        private readonly IRedisClientsManager _redisClientsManager;
        private readonly ISafeOperationService _safeOperationService;

        public RedisSafeOperationsService(
            IRedisClientsManager redisClientsManager,
            ISafeOperationService safeOperationService)
        {
            _redisClientsManager = redisClientsManager;
            _safeOperationService = safeOperationService;
        }

        public T Process<T>(Func<IRedisClient, T> request)
        {
            return _safeOperationService.SafeOperation(() =>
            {
                using (var client = _redisClientsManager.GetClient())
                {
                    return request(client);
                }
            });
        }

        public void Process(Action<IRedisClient> request)
        {
            _safeOperationService.SafeOperation(() =>
            {
                using (var client = _redisClientsManager.GetClient())
                {
                    request(client);
                }
            });
        }
    }
}
