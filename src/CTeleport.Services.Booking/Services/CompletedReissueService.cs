using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using CTeleport.Services.Booking.Core.Dto;
using CTeleport.Services.Booking.Shared.Models;
using CTeleport.Services.FlightStatus;
using CTeleport.Services.Helpers.Constants;
using CTeleport.Services.Helpers.Extensions;
using CTeleport.Services.Search.Shared.Models;

namespace CTeleport.Services.Booking.Services
{
    public class CompletedReissueService : ICompletedReissueService
    { 
        private readonly IProviderTicketStateCacheAdapter _providerTicketStateCacheAdapter;
        public CompletedReissueService(IProviderTicketStateCacheAdapter providerTicketStateCacheAdapter)
        {
            _providerTicketStateCacheAdapter = providerTicketStateCacheAdapter;
        }
        
        public async Task<ProviderTicket> GetTicketFromProviderAsync(string changeRequestId, 
            string ticketNumber, 
            Dictionary<string, string> reservationLocators, 
            string source,
            string providerKey)
        {
            var response = await _providerTicketStateCacheAdapter.GetTicketInfoAsync(new ProviderRetrieveTicketRequest
            {
                CorrelationId = changeRequestId,
                DocumentNumber = ticketNumber,
                Locators = reservationLocators,
                Source = source,
                SourceDetails = providerKey.IsSourceTravelportJson() ? new SourceDetails
                {
                    Version = ProviderKeyIndicators.JsonSource
                } : null
            });

            if (response.HasError)
            {
                throw new Exception($"Could not get ticket information from provider, number={ticketNumber}");
            }

            return response.Ticket;
        }
    }
}
