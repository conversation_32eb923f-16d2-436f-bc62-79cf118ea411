using System.Threading.Tasks;
using CTeleport.Services.Search.Shared.Models;

namespace CTeleport.Services.Booking.Services
{
    public interface IBookingRepriceService
    {
        /// <summary>
        /// Reprice a booking.
        /// </summary>
        /// <param name="searchAuthToken">SearchAuthToken</param>
        /// <param name="bookingId">BookingId</param>
        /// <returns>Validated FlightSolution</returns>
        Task<FlightSolutionDetails> RepriceBooking(string searchAuthToken, string bookingId);
    }
}