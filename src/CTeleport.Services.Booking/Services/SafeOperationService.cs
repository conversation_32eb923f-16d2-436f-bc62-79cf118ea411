using System;
using CTeleport.Services.Booking.Services;
using Polly;
using Serilog;

namespace CTeleport.Services.Booking.Services
{
    public class SafeOperationService : ISafeOperationService
    {
        private readonly ILogger _logger;
        private readonly Policy _policy;

        public SafeOperationService(ILogger logger)
        {
            _logger = logger;
            _policy = Policy.Handle<Exception>()
                .WaitAndRetry(3, retryAttempt => TimeSpan.FromSeconds(retryAttempt),
                    (exception, timeSpan, retryCount, context)
                        => _logger.Warning(exception, "Exception {ExceptionType} on {RetryCount} retry count", exception.GetType().Name, retryCount));
        }

        public T SafeOperation<T>(Func<T> request)
        {
            var policyResult = _policy.ExecuteAndCapture(request.Invoke);

            if (policyResult.FinalException != null)
            {
                _logger.Error(policyResult.FinalException, $"{nameof(SafeOperation)} failed");
                throw policyResult.FinalException;
            }

            return policyResult.Result;
        }

        public void SafeOperation(Action request)
            => SafeOperation(() =>
            {
                request?.Invoke();
                return true;
            });
    }
}
