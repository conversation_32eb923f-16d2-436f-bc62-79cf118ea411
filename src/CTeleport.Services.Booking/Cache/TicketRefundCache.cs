using System;
using CTeleport.Common.Redis;
using Serilog;
using ServiceStack.Caching;

namespace CTeleport.Services.Booking.Cache
{
    public class TicketRefundCache : SafeCacheBase, ITicketRefundCache
    {
        private readonly string KEY_PREFIX = "ticket:refund:";
        private readonly double MINUTES_TTL = 60;

        public TicketRefundCache(ICacheClient client, ILogger logger) : base(client, logger) { }

        public void Set(TicketRefundCacheEntry entry, string key)
        {
            SafeRequest(() => Client.Set(KEY_PREFIX + key, entry, TimeSpan.FromMinutes(MINUTES_TTL)));
        }

        public TicketRefundCacheEntry Get(string key)
        {
            return SafeRequest(() => Client.Get<TicketRefundCacheEntry>(KEY_PREFIX + key));
        }
    }
}
