using CTeleport.Services.Booking.Shared.Models;
using CTeleport.Services.Helpers.Helpers;

namespace CTeleport.Services.Booking.Extensions;

public static class SegmentExtensions
{
    public static string BuildFlightFingerprint(this Segment flightSegment)
    {
        return HashHelper.Build(flightSegment.Origin, flightSegment.Destination,
            flightSegment.Operator ?? flightSegment.Carrier,
            flightSegment.DepartureTime, flightSegment.ArrivalTime,
            flightSegment.CabinClass, flightSegment.EquipmentCode);
    }
}