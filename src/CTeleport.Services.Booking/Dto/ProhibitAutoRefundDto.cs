using System.ComponentModel.DataAnnotations;

namespace CTeleport.Services.Booking.Dto
{
    public class ProhibitAutoRefundDto
    {
        [Required]
        public string ReservationId { get; set; }

        /// <summary>
        /// Ticket number, if issued
        /// </summary>
        public string TicketNumber { get; set; }

        /// <summary>
        /// If true, the Ticket can't be refunded automatically
        /// </summary>
        public bool ProhibitAutoRefund { get; set; }
    }
}
