using System;
using System.Collections.Generic;
using CTeleport.Services.Booking.Enums;
using CTeleport.Services.Booking.Shared.Models;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

namespace CTeleport.Services.Booking.Dto.ExtendedStructuredReport;

public class ReservationDto
{
    /// <summary>
    /// Reservation id
    /// </summary>
    public string Id { get; set; }

    /// <summary>
    /// Reservation id
    /// </summary>
    public string BookingId { get; set; }

    /// <summary>
    /// Reservation passenger
    /// </summary>
    public ReservationPassenger Passenger { get; set; }

    /// <summary>
    /// Reservation state
    /// </summary>
    [JsonConverter(typeof(StringEnumConverter))]
    public ReservationState State { get; set; }

    /// <summary>
    /// Identifier of PNR source, the combination of GDS and PCC codes
    /// e.g. for C Teleport Galileo PCC in LV: "1G.8WG0"
    /// </summary>
    public string Source { get; set; }

    /// <summary>
    /// Reservation locators, including supplier locators.
    /// Locators variate for providers
    /// 1G: AirReservation, UniversalRecord, Provider
    /// </summary>
    public IReadOnlyDictionary<string, string> Locators { get; set; }

    /// <summary>
    /// Dictionary of supplier locators
    /// </summary>
    public IReadOnlyDictionary<string, string> SupplierLocators { get; set; }

    /// <summary>
    /// First segment departure date and time, UTC
    /// </summary>
    public DateTime DepartureAt { get; set; }

    /// <summary>
    /// Total travel time for each leg in a route, in minutes
    /// </summary>
    public IReadOnlyCollection<int> LegDurations { get; set; }

    /// <summary>
    /// Collection of flight legs
    /// </summary>
    public IReadOnlyCollection<ReservationLegDto> Legs { get; set; }

    /// <summary>
    /// Collection of segments grouped by leg
    /// </summary>
    public ICollection<ICollection<SegmentDto>> LegSegments { get; set; }

    /// <summary>
    /// Ticketing scheduled date and time, UTC
    /// </summary>
    public DateTime? TicketingAt { get; set; }

    /// <summary>
    /// Flag indicating that ticketing failed
    /// </summary>
    public bool? TicketingFailed { get; set; }

    /// <summary>
    /// Collection of tickets
    /// </summary>
    public IReadOnlyCollection<TicketDto> Tickets { get; set; }

    /// <summary>
    /// Date and time when this reservation was created, UTC
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Date and time when this reservation was updated, UTC
    /// </summary>
    public DateTime? UpdatedAt { get; set; }

    /// <summary>
    /// Date and time when this reservation was cancelled, UTC
    /// </summary>
    public DateTime? CancelledAt { get; set; }

    /// <summary>
    /// Tenant id
    /// </summary>
    public string TenantId { get; set; }

    /// <summary>
    /// If true, checks for class drop will be run.
    /// </summary>
    public bool ClassDropIsAllowed { get; set; }

    /// <summary>
    /// Indicates whether reservation can be cancelled (technically supported by provider)
    /// </summary>
    public bool CanCancel { get; set; }

    /// <summary>
    /// Indicates whether this reservation has a ticket associated or not. (If Ticketless==true, no ticket number is expected)
    /// </summary>
    public bool Ticketless { get; set; }

    /// <summary>
    /// Indicates whether reservation is virtual
    /// </summary>
    public bool IsVirtual { get; set; }

    /// <summary>
    /// Reference to original reservation id (virtual...)
    /// </summary>
    public string OriginalReservationId { get; set; }
}