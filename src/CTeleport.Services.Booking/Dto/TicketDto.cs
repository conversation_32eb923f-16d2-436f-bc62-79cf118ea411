using System;
using System.Collections.Generic;

namespace CTeleport.Services.Booking.Dto
{
    public class TicketDto
    {
        /// <summary>
        /// Ticket number, if issued
        /// </summary>
        public string Number { get; set; }

        /// <summary>
        /// Ticket state
        /// </summary>
        public string State { get; set; }

        /// <summary>
        /// Latest date and time for voiding, UTC
        /// </summary>
        public DateTime LastVoidAt { get; set; }
        
        /// <summary>
        /// Ticket issue date and time, UTC
        /// </summary>
        public DateTime IssuedAt { get; set; }

        /// <summary>
        /// Invoices numbers this ticket appears in
        /// </summary>
        [Obsolete("Remove after production migrated")] 
        public ICollection<string> InvoiceNumbers { get; set; }

        /// <summary>
        /// Credit note numbers this ticket appears in
        /// </summary>
        [Obsolete("Remove after production migrated")]
        public ICollection<string> CreditNoteNumbers { get; set; }

        /// <summary>
        /// If true, the Ticket can't be refunded automatically
        /// </summary>
        public bool ProhibitAutoRefund { get; set; }

    }
}