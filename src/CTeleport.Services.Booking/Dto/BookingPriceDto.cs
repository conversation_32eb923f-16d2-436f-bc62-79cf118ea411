using System.Collections.Generic;

namespace CTeleport.Services.Booking.Dto
{
    public class BookingPriceDto
    {
        /// <summary>
        /// Total price
        /// </summary>
        public decimal Total { get; set; }

        /// <summary>
        /// Price per mile
        /// </summary>
        public decimal PerMile { get; set; }

        /// <summary>
        /// Currency
        /// </summary>
        public string Ccy { get; set; }

        /// <summary>
        /// Price components with O-D pair as a key
        /// </summary>
        public Dictionary<string, decimal> Components { get; set; }
    }
}