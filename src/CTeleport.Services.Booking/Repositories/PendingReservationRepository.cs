using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using CTeleport.Common.Mongo;
using CTeleport.Services.Booking.Dto;
using CTeleport.Services.Booking.Enums;
using CTeleport.Services.Booking.Helpers;
using CTeleport.Services.Booking.Models;
using CTeleport.Services.Booking.Repositories.FilterConverters;
using CTeleport.Services.Booking.Repositories.Queries;
using CTeleport.Services.Booking.Services;
using MongoDB.Bson;
using MongoDB.Driver;
using MongoDB.Driver.Linq;
using Serilog;

namespace CTeleport.Services.Booking.Repositories
{
    public class PendingReservationRepository : SafeRepositoryBase, IPendingReservationRepository
    {
        protected IMongoCollection<PendingReservation> _collection;

        public PendingReservationRepository(IMongoDatabase database, ILogger logger) : base(database, logger)
        {
            _collection = database.PendingReservations();
        }

        public async Task AddAsync(PendingReservation pendingReservation)
            => await _collection.InsertOneAsync(pendingReservation);

        public async Task<PendingReservation> GetAsync(string id)
            => await SafeReadAsync(_collection.AsQueryable()
                .Where(b => b.ReservationId == id)
                .FirstOrDefaultAsync());

        public async Task<IEnumerable<PendingReservation>> GetAllAsync(Expression<Func<PendingReservation, bool>> filter, int limit = 10)
            => await SafeReadAsync(
                _collection.AsQueryable()
                    .Where(filter)
                    .OrderBy(r => r.CheckedAt)
                    .Take(limit)
                    .ToListAsync());

        public async Task UpdateAsync(PendingReservation pendingReservation)
            => await _collection.ReplaceOneAsync(x => x.ReservationId == pendingReservation.ReservationId, pendingReservation);
    }
}