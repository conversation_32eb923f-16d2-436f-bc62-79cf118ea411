using System.Threading.Tasks;

using CTeleport.Services.Booking.Sagas;

namespace CTeleport.Services.Booking.Repositories
{
    // TODO: rewrite unit tests -> delete
    public interface IBookingSagaRepository
    {
        Task<CreateBooking> GetAsync(string id);
        Task<CreateBooking> GetByPaymentIdAsync(string paymentId);
        Task AddAsync(CreateBooking saga);
        Task UpdateAsync(CreateBooking saga);
    }
}