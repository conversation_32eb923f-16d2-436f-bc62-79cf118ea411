using CTeleport.Common.Exceptions;
using CTeleport.Common.Messaging.Services;
using CTeleport.Messages.Commands.Reservations;
using CTeleport.Services.Booking.Models;
using CTeleport.Services.Booking.Services;
using Serilog;
using System.Threading.Tasks;

namespace CTeleport.Services.Booking.Handlers
{
    public class ReservationIrregularitiesHandler 
        : ICommandHandler<RegisterCancelledSegments>,
          ICommandHandler<RegisterRescheduledSegments>
    {
        private readonly IHandlerFactory _handlerFactory;
        private readonly IBookingService _bookingService;
        private readonly ILogger _logger;

        public ReservationIrregularitiesHandler(
            IHandlerFactory handlerFactory,
            IBookingService bookingService,
            ILogger logger)
        {
            _handlerFactory = handlerFactory;
            _bookingService = bookingService;
            _logger = logger;
        }

        public async Task HandleAsync(RegisterCancelledSegments command)
        {
            Reservation reservation = null;

            await _handlerFactory
                .Create(command)
                .Validate(async () =>
                {
                    reservation = await _bookingService.GetReservationAsync(command.ReservationId);
                    if (reservation == null)
                    {
                        throw new ValidationException("Couldn't find reservation");
                    }
                })
                .Run(async () =>
                {
                    await RegisterCancellationAsync(reservation);
                })
                .OnError((ex) =>
                {
                    _logger.Error(ex, "An error occur when processing {Command} command", nameof(RegisterCancelledSegments));
                })
                .Lock("Reservation/" + command.ReservationId)
                .ExecuteAsync();
        }

        public async Task HandleAsync(RegisterRescheduledSegments command)
        {
            Reservation reservation = null;

            await _handlerFactory
                .Create(command)
                .Validate(async () =>
                {
                    reservation = await _bookingService.GetReservationAsync(command.ReservationId);
                    if (reservation == null)
                    {
                        throw new ValidationException("Couldn't find reservation");
                    }
                })
                .Run(async () =>
                {
                    await RegisterRescheduleAsync(reservation);
                })
                .OnError((ex) =>
                {
                    _logger.Error(ex, "An error occur when processing {Command} command", nameof(RegisterRescheduledSegments));
                })
                .Lock("Reservation/" + command.ReservationId)
                .ExecuteAsync();
        }

        private async Task RegisterCancellationAsync(Reservation reservation)
        {
            var irregularities = reservation.Irregularities ?? new ReservationIrregularities { };
            irregularities.Cancelled = true;
            reservation.Irregularities = irregularities;

            await _bookingService.UpdateReservationIrregularitiesAsync(reservation);
        }

        private async Task RegisterRescheduleAsync(Reservation reservation)
        {
            var irregularities = reservation.Irregularities ?? new ReservationIrregularities { };
            irregularities.Rescheduled = true;
            reservation.Irregularities = irregularities;

            await _bookingService.UpdateReservationIrregularitiesAsync(reservation);
        }
    }
}