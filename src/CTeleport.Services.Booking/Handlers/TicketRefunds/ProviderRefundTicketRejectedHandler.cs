using System.Threading.Tasks;
using CTeleport.Common.Authorization.Services;
using CTeleport.Services.Booking.Services;
using CTeleport.Common.Messaging.Services;
using CTeleport.Messages.Commands;
using CTeleport.Messages.Commands.Tickets;
using CTeleport.Messages.Events.Tickets;
using CTeleport.Services.Booking.Cache;
using CTeleport.Services.Booking.Configuration;
using CTeleport.Services.Booking.Core;
using CTeleport.Services.Booking.Extensions;
using CTeleport.Services.Helpers;
using Serilog;
using Serilog.Context;
using ValidationException = CTeleport.Common.Exceptions.ValidationException;

namespace CTeleport.Services.Booking.Handlers
{
    /// <summary>
    /// Relays information about ticket refund failure reported by provider by dispatching RefundTicketRejected event
    /// </summary>
    public class ProviderRefundTicketRejectedHandler : IEventHandler<ProviderRefundTicketRejected>
    {
        private readonly IHandlerFactory _handlerFactory;
        private readonly IMessageDispatcher _dispatcher;
        private readonly IBookingService _bookingService;
        private readonly TicketStateValidationOptions _options;

        private readonly ITicketRefundCache _cache; // TODO: remove this, information about the user is in general messaging context
        private readonly ILogger _logger;
        private readonly IServiceContext _context;

        public ProviderRefundTicketRejectedHandler(IHandlerFactory handlerFactory, IMessageDispatcher dispatcher, IBookingService bookingService,
            TicketStateValidationOptions options, ITicketRefundCache cache, ILogger logger, IServiceContext context)
        {
            _handlerFactory = handlerFactory;
            _dispatcher = dispatcher;
            _bookingService = bookingService;
            _options = options;
            _cache = cache;
            _logger = logger;
            _context = context;
        }

        public async Task HandleAsync(ProviderRefundTicketRejected @event)
        {
            using (LogContext.PushProperty("ReservationId", @event.ReservationId))
            using (LogContext.PushProperty("RequestId", @event.RequestId))
            using (LogContext.PushProperty("ProviderRefundTicketRejected", @event, true))
            {
                await HandleTicketRefund(@event);
            }
        }

        private async Task HandleTicketRefund(ProviderRefundTicketRejected @event)
        {
            _logger.Information("Handling ProviderRefundTicketRejected");

            Models.Reservation reservation = null;
            var userName = _context?.User?.Name;
            var autoRefund = false;
            var errorCode = @event.Code;
            var errorMessage = @event.Reason;

            await _handlerFactory
                .Create(@event)
                .Validate(async () =>
                {
                    _logger.Information("Validating event");

                    reservation = await _bookingService.GetReservationAsync(@event.ReservationId);

                    if (reservation == null)
                    {
                        throw new ValidationException("Reservation is not found");
                    }

                    var cacheEntry = _cache.Get(@event.RequestId);
                    if (cacheEntry != null)
                    {
                        autoRefund = cacheEntry.AutoRefund;
                    }

                    _logger.Information("Validation complete");
                })
                .Run(async () =>
                {
                    _logger.Information("Processing rejected event");

                    if (errorCode == "refund_rejected_use_void_instead")
                    {
                        _logger.Information("Reached logic 'VOID instead Refund', because {ErrorCode}", errorCode);

                        var providerVoidTicket = new ProviderVoidTicket
                        {
                            Locators = reservation.Locators,
                            Number = @event.Number,
                            Request = Request.New<ProviderVoidTicket>(@event.RequestId),
                            ReservationId = @event.ReservationId,
                            Source = reservation.Source
                        };

                        await _dispatcher.DispatchAsync(providerVoidTicket, suffix: reservation.Source.GetProvider());

                        _logger.Information("Dispatched {@ProviderVoidTicket}", providerVoidTicket);
                    }

                    if (autoRefund && @event.Code == ErrorCodes.TicketIsUsed)
                    {
                        // Provider reported that ticket is used, it can be because or ARPT status
                        // we'll either wait to try again or fail with Blocked coupons reason, 
                        var options = _options.For(reservation);

                        if (options.IsAfterCoolDownPeriod())
                        {
                            errorCode = AutoRefundErrorCodes.TicketIsBlocked;
                            errorMessage = "Ticket coupons are blocked.";
                        }
                        else
                        {
                            errorCode = AutoRefundErrorCodes.AutorefundCooldownRequired;
                            errorMessage = $"Temporarily blocked coupons. Waiting for {options.CooldownHoursAfterDeparture} hours after departure.";
                        }
                    }

                    _logger.Information("Dispatching RefundTicketRejected");

                    await _dispatcher.DispatchAsync(
                        new RefundTicketRejected(
                            @event.RequestId,
                            reservation.BookingId,
                            reservation.Id,
                            errorCode,
                            errorMessage,
                            LocatorsHelper.GetProviderCode(reservation.Locators),
                            @event.Number,
                            reservation.DepartureAt,
                            userName,
                            autoRefund,
                            reservation.Source));
                })
                .OnError(async ex =>
                {
                    _logger.Error(ex, "Could not handle ProviderRefundTicketRejected for {TicketNumber}", @event.Number);

                    if (reservation != null)
                    {
                        await _dispatcher.DispatchAsync(
                            new RefundTicketRejected(
                                @event.RequestId,
                                reservation.BookingId,
                                reservation.Id,
                                errorCode,
                                errorMessage,
                                LocatorsHelper.GetProviderCode(reservation.Locators),
                                @event.Number,
                                reservation.DepartureAt,
                                userName,
                                autoRefund,
                                reservation.Source));   
                    }
                })
                .Lock("Reservation/" + @event.ReservationId)
                .ExecuteAsync();
        }
    }
}