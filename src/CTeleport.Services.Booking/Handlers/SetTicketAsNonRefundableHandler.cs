using CTeleport.Common.Exceptions;
using CTeleport.Common.Messaging.Services;
using CTeleport.Messages.Commands.Bookings;
using CTeleport.Messages.Commands.Tickets;
using CTeleport.Messages.Events.Tickets;
using CTeleport.Services.Booking.Core;
using CTeleport.Services.Booking.Services;
using CTeleport.Services.Helpers;
using Serilog;
using System.Linq;
using System.Threading.Tasks;

namespace CTeleport.Services.Booking.Handlers
{
    public class SetTicketAsNonRefundableHandler : ICommandHandler<SetTicketAsNonRefundable>
    {
        private readonly IHandlerFactory _handlerFactory;
        private readonly IBookingService _bookingService;
        private readonly IMessageDispatcher _dispatcher;
        private readonly ILogger _logger;

        public SetTicketAsNonRefundableHandler(
            IHandlerFactory handlerFactory,
            IBookingService bookingService,
            IMessageDispatcher dispatcher,
            ILogger logger)
        {
            _handlerFactory = handlerFactory;
            _bookingService = bookingService;
            _dispatcher = dispatcher;
            _logger = logger;
        }

        public async Task HandleAsync(SetTicketAsNonRefundable command)
        {
            Models.Booking booking = null;
            Models.Reservation reservation = null;
            Models.Ticket ticket = null;

            await _handlerFactory
                .Create(command)
                .Validate(async () =>
                {
                    reservation = await _bookingService.GetReservationAsync(command.ReservationId);

                    if (reservation == null)
                    {
                        throw new ValidationException("Reservation not found");
                    }

                    booking = await _bookingService.GetBookingAsync(reservation.BookingId);

                    if (booking == null)
                    {
                        throw new ValidationException("Booking not found");
                    }
                    if (!reservation.Ticketless) {
                        // check that we have tickets to update
                        ticket = reservation.Tickets.First(t => t.Number == command.Number);

                        if (ticket == null)
                        {
                            throw new ValidationException("Ticket not found");
                        }
                    }
                })
                .Run(async () =>
                {
                    if (!reservation.Ticketless)
                    {
                        await _bookingService.SetNonRefundableAsync(reservation.Id, command.Number, true);
                    }
                })
                .OnSuccess(async () =>
                {
                    await _dispatcher.DispatchAsync(new RefundTicketRejected(
                        command.Request.Id,
                        booking.Id,
                        reservation.Id,
                        AutoRefundErrorCodes.TicketIsNonRefundable,
                        command.Reason,
                        LocatorsHelper.GetProviderCode(reservation.Locators),
                        command.Number,
                        reservation.DepartureAt,
                        command.User?.Name ?? command.User?.Email,
                        false,
                        reservation.Source));

                    await _dispatcher.DispatchAsync(new ResetBookingState
                    {
                        Request = command.Request,
                        User = command.User,
                        BookingId = booking.Id
                    });
                })
                .OnError((ex) =>
                {
                    _logger.Error(ex, "An error occur when processing {Event} event", nameof(SetTicketAsNonRefundable));
                })
                .ExecuteAsync();
        }
    }
}