using CTeleport.Common.Messaging.Services;
using CTeleport.Messages.Commands.Bookings;
using CTeleport.Services.Booking.Services;
using Serilog;
using System.Threading.Tasks;

namespace CTeleport.Services.Booking.Handlers
{
    public class SetBookingAsNoShowHandler : ICommandHandler<SetBookingAsNoShow>
    {
        private readonly IHandlerFactory _handlerFactory;
        private readonly IBookingService _bookingService;
        private readonly ILogger _logger;
        

        public SetBookingAsNoShowHandler(
            IHandlerFactory handlerFactory,
            IBookingService bookingService,
            ILogger logger)
        {
            _handlerFactory = handlerFactory;
            _bookingService = bookingService;
            _logger = logger;
        }

        public async Task HandleAsync(SetBookingAsNoShow command)
        {
            await _handlerFactory
                .Create(command)
                .Run(async () =>
                {
                    await _bookingService.SetBookingAsNoShowAsync(command.BookingId);
                })
                .OnError((ex) =>
                {
                    _logger.Error(ex, $"Error occured while setting booking {command.BookingId} as no-show");
                })
                .ExecuteAsync();
        }
    }
}
