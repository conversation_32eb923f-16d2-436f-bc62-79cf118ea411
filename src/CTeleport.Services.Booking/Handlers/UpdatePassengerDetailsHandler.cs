using AutoMapper;
using CTeleport.Common.Exceptions;
using CTeleport.Common.Messaging.Services;
using CTeleport.Messages.Commands.Bookings;
using CTeleport.Messages.Commands.Reservations;
using CTeleport.Messages.Events.Bookings;
using CTeleport.Messages.Events.Reservations;
using CTeleport.Services.Booking.Core;
using CTeleport.Services.Booking.Enums;
using CTeleport.Services.Booking.Models;
using CTeleport.Services.Booking.Services;
using CTeleport.Services.Booking.Shared.Enums;
using CTeleport.Services.Helpers;
using CTeleport.Services.Travelers.Contracts.Events;
using CTeleport.Services.Travelers.Contracts.Models;
using CTeleport.Services.Travelers.Contracts.Models.Enums;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CTeleport.Services.Booking.Handlers
{
    public class UpdatePassengerDetailsHandler 
        : ICommandHandler<UpdatePassengerDetails>,
        IEventHandler<ProviderUpdatePassengerDetailsCompleated>,
        IEventHandler<ProviderUpdatePassengerDetailsRejected>
    {
        private readonly IHandlerFactory _handlerFactory;
        private readonly IBookingService _bookingService;
        private readonly IMessageDispatcher _dispatcher;
        private readonly IAuthService _authService;
        private readonly IMapper _mapper;
        private readonly ILogger _logger;

        public UpdatePassengerDetailsHandler(
            IHandlerFactory handlerFactory,
            IBookingService bookingService,
            IMessageDispatcher dispatcher,
            IAuthService authService,
            IMapper mapper,
            ILogger logger)
        {
            _handlerFactory = handlerFactory;
            _bookingService = bookingService;
            _dispatcher = dispatcher;
            _authService = authService;
            _mapper = mapper;
            _logger = logger;
        }

        public async Task HandleAsync(UpdatePassengerDetails command)
        {
            Models.Booking booking = null;
            IEnumerable<Reservation> reservations = null;

            await _handlerFactory
                .Create(command)
                .Validate(async () =>
                {
                    booking = await _bookingService.GetBookingAsync(command.BookingId);

                    if (booking == null)
                    {
                        throw new ValidationException("Booking not found.");
                    }

                    await _authService.ValidateIfBookingCanBeManagedOrFailAsync(booking);

                    if (booking.State == BookingState.Cancelled || booking.State == BookingState.Declined)
                    {
                        throw new ValidationException("Booking is already cancelled.");
                    }

                    if (booking.DepartureAt - DateTime.UtcNow < TimeSpan.FromHours(24))
                    {
                        throw new ValidationException(OperationCodes.DepartureTooCloseToAllowModifications, "Departure time is less than 24 hours away.");
                    }

                    reservations = await _bookingService.GetBookingReservationsAsync(command.BookingId);

                    if (reservations.Count(r => r.State == ReservationState.Active) == 0)
                    {
                        throw new ValidationException("Booking has no active reservations");
                    }
                })
                .Run(async () =>
                {
                    await _bookingService.UpdateBookingPassengerAsync(command.BookingId, _mapper.Map<MutablePassengerDetails>(command.Passenger));
                    booking = await _bookingService.GetBookingAsync(command.BookingId);

                    var tasks = reservations
                        .Where(r => !r.IsVirtual && r.State == ReservationState.Active)
                        .Select(r =>
                        {
                            var message = new ProviderUpdatePassengerDetails
                            {
                                Source = r.Source,
                                Locators = r.Locators,
                                ReservationId = r.Id,
                                Passenger = _mapper.Map<Messages.Commands.Models.PassengerDetails>(booking.Passenger),
                                Request = command.Request
                            };

                            return _dispatcher.DispatchAsync(message, suffix: r.Source.GetProvider());
                        });

                    await Task.WhenAll(tasks);
                })
                .OnSuccess(async () =>
                {
                    await _dispatcher.DispatchAsync(new PassengerDetailsUpdated(command.Request.Id, command.BookingId));

                    var passengerDetailsUpdateEvent = CreateTravelerDetailsUpdateEvent(booking);
                    await _dispatcher.DispatchAsync(passengerDetailsUpdateEvent);
                })
                .OnCustomError(async (ex) =>
                {
                    await _dispatcher.DispatchAsync(new UpdatePassengerDetailsRejected(
                        command.Request.Id,
                        command.BookingId,
                        ex.Code ?? OperationCodes.Error,
                        ex.Message));
                })
                .OnError(async (ex) =>
                {
                    _logger.Error(ex, "Error occured while updating passenger for booking {Id}", command.BookingId);
                    
                    await _dispatcher.DispatchAsync(new UpdatePassengerDetailsRejected(
                        command.Request.Id,
                        command.BookingId,
                        OperationCodes.Error,
                        ex.Message));
                })
                .ExecuteAsync();
        }

        private static TravelerDetailsUpdate CreateTravelerDetailsUpdateEvent(Models.Booking booking)
        {
            var docType = booking.Passenger.DocType switch
            {
                DocumentType.Passport => DocType.Passport,
                _ => DocType.Passport
            };
            var document = new Document(
                Type: docType,
                Number: booking.Passenger.DocNumber,
                Country: booking.Passenger.DocCountry,
                ExpireDate: booking.Passenger.DocExpire);
            
            var emails = string.IsNullOrWhiteSpace(booking.Passenger.Email)
                ? Array.Empty<Email>()
                : new[]
                {
                    new Email(
                        Type: ContactType.Work,
                        Value: booking.Passenger.Email)
                };

            var phones = string.IsNullOrWhiteSpace(booking.Passenger.Phone)
                ? Array.Empty<Phone>()
                : new[]
                {
                    new Phone(
                        Type: ContactType.Work,
                        Value: booking.Passenger.Phone)
                };

            var gender = booking.Passenger.Gender switch
            {
                Search.Shared.Enums.Gender.Female => "F",
                _ => "M"
            };

            var dateOfBirth = DateTime.TryParse(
                booking.Passenger.DateOfBirth, out var dob) ? dob : default;
            
            var loyaltyPrograms = booking.FrequentFlyerNumbers?
                .Select(kv => new LoyaltyProgram(
                    Type: kv.Key switch
                    {
                        "hotels" => LoyaltyProgramType.Hotel,
                        _ => LoyaltyProgramType.Flight
                    },
                    Code: kv.Value?.Code,
                    Carrier: kv.Value?.Carrier,
                    Group: kv.Value?.ProgramName,
                    Number: kv.Value?.Number)
                ).ToArray() ?? Array.Empty<LoyaltyProgram>();

            var traveler = new Traveler(
                Id: booking.Passenger.Id,
                FirstName: booking.Passenger.FirstName,
                LastName: booking.Passenger.LastName,
                DateOfBirth: dateOfBirth,
                Nationality: booking.Passenger.Nationality,
                Gender: gender,
                Emails: emails,
                Phones: phones,
                LoyaltyPrograms: loyaltyPrograms,
                Documents: new[] { document }
            );

            var @event = new TravelerDetailsUpdate(
                Traveler: traveler,
                BookingId: booking.Id,
                TenantId: booking.TenantId);
            return @event;
        }


        public async Task HandleAsync(ProviderUpdatePassengerDetailsCompleated @event)
        {
            Reservation reservation = null;

            await _handlerFactory
                .Create(@event)
                .Validate(async () =>
                {
                    reservation = await _bookingService.GetReservationAsync(@event.ReservationId);

                    if (reservation == null)
                    {
                        throw new ValidationException("Reservation is not found");
                    }
                })
                .Run(async () =>
                {
                    _logger.Error("Error occured while updating passenger for booking {Id}", reservation.BookingId);
                    await _dispatcher.DispatchAsync(new PassengerDetailsUpdated(@event.RequestId, reservation.BookingId));
                })
                .OnError((ex) =>
                {
                    _logger.Error(ex, "An error occur when processing {Event} event", nameof(ProviderUpdatePassengerDetailsCompleated));
                })
                .Lock("Reservation/" + @event.ReservationId)
                .ExecuteAsync();
        }

        public async Task HandleAsync(ProviderUpdatePassengerDetailsRejected rejectedEvent)
        {
            Reservation reservation = null;

            await _handlerFactory
                .Create(rejectedEvent)
                .Validate(async () =>
                {
                    reservation = await _bookingService.GetReservationAsync(rejectedEvent.ReservationId);

                    if (reservation == null)
                    {
                        throw new ValidationException("Reservation is not found");
                    }
                })
                .Run(async () =>
                {
                    _logger.Error("Error occured while updating passenger for booking {Id}", reservation.BookingId);

                    await _dispatcher.DispatchAsync(new UpdatePassengerDetailsRejected(
                        rejectedEvent.RequestId, 
                        reservation.BookingId, 
                        OperationCodes.Error, 
                        rejectedEvent.Reason));
                })
                .OnError((ex) =>
                {
                    _logger.Error(ex, "An error occur when processing {Event} event", nameof(ProviderUpdatePassengerDetailsRejected));
                })
                .Lock("Reservation/" + rejectedEvent.ReservationId)
                .ExecuteAsync();
        }
    }
}