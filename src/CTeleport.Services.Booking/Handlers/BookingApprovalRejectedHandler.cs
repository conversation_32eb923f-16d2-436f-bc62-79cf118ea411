using AutoMapper;
using CTeleport.Common.Exceptions;
using CTeleport.Common.Messaging.Services;
using CTeleport.Messages.Commands;
using CTeleport.Messages.Commands.Reservations;
using CTeleport.Messages.Events.Bookings;
using CTeleport.Services.Booking.Enums;
using CTeleport.Services.Booking.Services;
using CTeleport.Services.Booking.Services.Interfaces;
using Serilog;
using System;
using System.Linq;
using System.Threading.Tasks;
using BookingSaga = CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Booking;

namespace CTeleport.Services.Booking.Handlers
{
    public class BookingApprovalRejectedHandler : IEventHandler<BookingApprovalRejected>
    {
        private readonly IHandlerFactory _handlerFactory;
        private readonly IBookingService _bookingService;
        private readonly IBookingSagaService _bookingSagaService;
        private readonly IBookingVariantService _bookingVariantService;
        private readonly IMessageDispatcher _dispatcher;
        private readonly ILogger _logger;
        private readonly IMapper _mapper;
        private readonly IBookingMetricsService _metricsService;

        public BookingApprovalRejectedHandler(
            IHandlerFactory handlerFactory,
            IBookingService bookingService,
            IBookingSagaService bookingSagaService,
            IBookingVariantService bookingVariantService,
            IMessageDispatcher dispatcher,
            ILogger logger,
            IMapper mapper,
            IBookingMetricsService metricsService)
        {
            _handlerFactory = handlerFactory;
            _bookingService = bookingService;
            _bookingSagaService = bookingSagaService;
            _bookingVariantService = bookingVariantService;
            _dispatcher = dispatcher;
            _logger = logger;
            _mapper = mapper;
            _metricsService = metricsService;
        }

        public Task HandleAsync(BookingApprovalRejected @event)
        {
            _logger.ForContext("CorrelationId", @event.BookingId).Information("{EventName} Start processing", nameof(BookingApprovalRejected));

            Models.Booking booking = null;
            BookingSaga saga = null;

            return _handlerFactory.Create(@event)
                .Validate(async () =>
                {
                    booking = await _bookingService.GetBookingAsync(@event.BookingId);
                    if (booking == null)
                    {
                        throw new NotFoundException("Booking not found");
                    }

                    if (booking.State != BookingState.ApprovalRequired)
                    {
                        throw new ValidationException($"Invalid booking state {booking.State}. Must be {BookingState.ApprovalRequired}");
                    }

                    saga = await _bookingSagaService.GetAsync(@event.BookingId);

                    if (saga == null)
                    {
                        throw new Exception("Saga was not found.");
                    }

                    if (saga.SagaState is not Domain.Aggregates.BookingAggregate.Enums.BookingSagaState.InApproval)
                    {
                        throw new ValidationException($"Invalid booking saga state {saga.SagaState}. Must be {CreateBookingSagaState.InApproval}");
                    }
                })
                .Run(async () =>
                {
                    var reservations = await _bookingService.GetBookingReservationsAsync(@event.BookingId);

                    await Task.WhenAll(reservations
                        .Where(r => r.State == ReservationState.Active)
                        .Select(reservation =>
                            _dispatcher.DispatchAsync(new CancelReservation
                            {
                                Request = Request.New<CancelReservation>(@event.RequestId),
                                ReservationId = reservation.Id,
                                User = @event.RejectedBy
                            })
                        ));

                    _logger.ForContext("CorrelationId", @event.BookingId).Information("{EventName} CancelReservation sent", nameof(BookingApprovalRejected));

                    saga = await _bookingSagaService.ChangeStateAsync(saga.Id, Domain.Aggregates.BookingAggregate.Enums.BookingSagaState.Rejected);

                    _logger.ForContext("CorrelationId", @event.BookingId).Information("{EventName} Saga updated", nameof(BookingApprovalRejected));

                    await _bookingService.SetAsDeclinedByAsync(@event.BookingId,
                            _mapper.Map<Search.Shared.Models.User>(@event.RejectedBy), @event.ReasonText);
                        
                    _logger.ForContext("CorrelationId", @event.BookingId).Information("{EventName} Finish with Declined", nameof(BookingApprovalRejected));

                    _metricsService.IncrementBookingSagaRejectedCounter(@event.TenantId, @event.Reason);
                    _metricsService.DecrementActiveBookingSagaCounter(@event.TenantId);
                })
                .OnError(e =>
                {
                    _logger.ForContext("CorrelationId", @event.BookingId).Error(e, "Could not handle BookingApprovalRejected event {@Event}", @event);
                    return Task.CompletedTask;
                })
                .Lock("Bookings/" + @event.BookingId)
                .ExecuteAsync();
        }
    }
}
