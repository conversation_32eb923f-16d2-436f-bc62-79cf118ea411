using CTeleport.Common.Exceptions;
using CTeleport.Common.Messaging.Services;
using CTeleport.Messages.Commands.Reservations;
using CTeleport.Messages.Events.Reservations;
using CTeleport.Services.Booking.Services;
using Serilog;
using System;
using System.Threading.Tasks;

namespace CTeleport.Services.Booking.Handlers
{
    public class ReservationFareUpdatedHandler : IEventHandler<ReservationFareUpdated>
    {
        private readonly IHandlerFactory _handlerFactory;
        private readonly IBookingService _bookingService;
        private readonly IMessageDispatcher _dispatcher;
        private readonly ILogger _logger;

        public ReservationFareUpdatedHandler(
            IHandlerFactory handlerFactory,
            IBookingService bookingService,
            IMessageDispatcher dispatcher,
            ILogger logger)
        {
            _handlerFactory = handlerFactory;
            _bookingService = bookingService;
            _dispatcher = dispatcher;
            _logger = logger;
        }

        public async Task HandleAsync(ReservationFareUpdated @event)
        {
            Models.Reservation reservation = null;

            await _handlerFactory
                .Create(@event)
                .Validate(async () =>
                {
                    reservation = await _bookingService.GetReservationAsync(@event.ReservationId);

                    if (reservation == null)
                    {
                        throw new ValidationException("Reservation is not found");
                    }
                })
                .Run(async () =>
                {
                    await _bookingService.SetRefreshFareTimeAsync(reservation.Id, DateTime.UtcNow.AddHours(24));
                })
                .OnError((ex) =>
                {
                    _logger.Error(ex, "Could not update fare refresh time for reservation");
                })
                .Always(async () =>
                {
                    await _dispatcher.DispatchAsync(new ResetTicketingTime(reservation.Id));
                })
                .ExecuteAsync();
        }
    }
}