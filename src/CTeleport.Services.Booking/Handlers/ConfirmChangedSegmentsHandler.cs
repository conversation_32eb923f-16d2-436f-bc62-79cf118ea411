using AutoMapper;
using CTeleport.Common.Exceptions;
using CTeleport.Common.Helpers;
using CTeleport.Common.Messaging.Services;
using CTeleport.Messages.Commands;
using CTeleport.Messages.Commands.Reservations;
using CTeleport.Messages.Events.Reservations;
using CTeleport.Services.Booking.Core.Dto;
using CTeleport.Services.Booking.Core.Interfaces;
using CTeleport.Services.Booking.Helpers;
using CTeleport.Services.Booking.Models;
using CTeleport.Services.Booking.Services;
using CTeleport.Services.Booking.Shared.Models;
using CTeleport.Services.Helpers;
using Serilog;
using ServiceStack.Text;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CTeleport.Services.Booking.Handlers
{
    public class ConfirmChangedSegmentsHandler : ICommandHandler<ConfirmChangedSegments>
    {
        private readonly IProviderConfirmReservationService _confirmReservationService;
        private readonly IHandlerFactory _handlerFactory;
        private readonly IBookingService _bookingService;
        private readonly IMessageDispatcher _dispatcher;
        private readonly ILogger _logger;
        private readonly IMapper _mapper;

        public ConfirmChangedSegmentsHandler(
            IProviderConfirmReservationService confirmReservationService,
            IHandlerFactory handlerFactory,
            IBookingService bookingService,
            IMessageDispatcher dispatcher,
            IMapper mapper,
            ILogger logger)
        {
            _confirmReservationService = confirmReservationService;
            _handlerFactory = handlerFactory;
            _bookingService = bookingService;
            _dispatcher = dispatcher;
            _mapper = mapper;
            _logger = logger;
        }

        public async Task HandleAsync(ConfirmChangedSegments command)
        {
            Reservation reservation = null;

            await _handlerFactory
                .Create(command)
                .Validate(async () =>
                {
                    reservation = await _bookingService.GetReservationAsync(command.ReservationId);
                    if (reservation == null)
                    {
                        throw new ValidationException("Couldn't find reservation");
                    }
                })
                .Run(async () =>
                {
                    _logger.Information("Trying to confirm segments for reservation {Locator} after schedule changed",
                        LocatorsHelper.GetProviderCode(reservation.Locators));

                    var providerReservation = await _confirmReservationService.ConfirmReservationAsync(new ConfirmReservationRequest
                    {
                        Source = reservation.Source,
                        Locators = reservation.Locators
                    });

                    if (providerReservation.HasError)
                    {
                        throw new ValidationException($"Cannot confirm reservation due to provider error: {providerReservation.Error.ErrorMessage}");
                    }
                    
                    var segmentsNew = _mapper.Map<List<Segment>>(providerReservation.FlightReservation.Segments).OrderBy(s => s.DepartureTimestampUtc).ToList();
                    var changes = ReservationHelper.BuildSegmentsDiffString(reservation, segmentsNew);

                    await UpdateSegmentsAsync(reservation, segmentsNew);

                    var hasTicket = reservation.Tickets.Any();
                    if (!hasTicket)
                    {
                        _logger.Information("Refreshing reservation {Locator} fare after schedule changed",
                            LocatorsHelper.GetProviderCode(reservation.Locators));
                        await _dispatcher.DispatchAsync(new RefreshReservationFare
                        {
                            Request = new Request {Id = Id.New()},
                            ReservationId = reservation.Id
                        });
                    }
                     
                    await _dispatcher.DispatchAsync(new ChangedSegmentsConfirmationCompleted
                    {
                        BookingId = reservation.BookingId,
                        Locator = LocatorsHelper.GetProviderCode(reservation.Locators),
                        PaxLastname = reservation.Passenger.LastName,
                        HasTicket = hasTicket, 
                        Changes = changes
                    });
                })
                .OnCustomError(async (e) =>
                {
                    _logger.Error(e, "Couldn't confirm segments schedule change for reservation {Locator}", LocatorsHelper.GetProviderCode(reservation.Locators));
                    await _dispatcher.DispatchAsync(new ChangedSegmentsConfirmationRejected
                    {
                        BookingId = reservation.BookingId,
                        Locator = LocatorsHelper.GetProviderCode(reservation.Locators),
                        PaxLastname = reservation.Passenger.LastName,
                        DepartureAt = reservation.DepartureAt
                    });
                })
                .Lock("Reservation/" + command.ReservationId)
                .ExecuteAsync();
        }


        //TODO make handler for that, use extra call
        private async Task UpdateSegmentsAsync(Reservation reservation, List<Segment> segmentsNew)
        {
            foreach (var leg in reservation.LegSegments)
            {
                var segments = leg.ToList();

                // TODO Mind about confirm flight number and whole segments changes
                foreach (var segment in segments)
                {
                    var segmentNew = segmentsNew.First(s => s.Origin == segment.Origin && s.Destination == segment.Destination);
                    segment.DepartureDate = segmentNew.DepartureDate;
                    segment.DepartureTime = segmentNew.DepartureTime;
                    segment.DepartureTimestamp = segmentNew.DepartureTimestamp;
                    segment.DepartureTimestampUtc = segmentNew.DepartureTimestampUtc;
                    segment.ArrivalDate = segmentNew.ArrivalDate;
                    segment.ArrivalTime = segmentNew.ArrivalTime;
                    segment.ArrivalTimestamp = segmentNew.ArrivalTimestamp;
                    segment.ArrivalTimestampUtc = segmentNew.ArrivalTimestampUtc;
                }

                for (var i = 0; i < leg.Count - 1; i++)
                {
                    var departure = segments[i + 1].DepartureTimestampUtc.FromUnixTime();
                    var arrival = segments[i].ArrivalTimestampUtc.FromUnixTime();
                    segments[i].ConnectionDuration = (int)departure.Subtract(arrival).TotalMinutes;
                }
            }

            reservation.DepartureAt = reservation.LegSegments.First().First().DepartureTimestampUtc.FromUnixTime();
            await _bookingService.UpdateReservationSegmentsAsync(reservation);
        }
    }
}