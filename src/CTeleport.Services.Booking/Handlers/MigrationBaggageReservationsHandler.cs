using CTeleport.Common.Messaging.Services;
using CTeleport.Services.Booking.Commands;
using CTeleport.Services.Booking.Models;
using CTeleport.Services.Booking.Repositories.Queries;
using MongoDB.Driver;
using Serilog;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CTeleport.Services.Booking.Handlers
{
    public class MigrationBaggageReservationsHandler : ICommandHandler<RunBaggageReservationsMigration>
    {
        private readonly IHandlerFactory _handlerFactory;
        private readonly IMongoDatabase _database;
        private readonly ILogger _logger;

        public MigrationBaggageReservationsHandler(
            IHandlerFactory handlerFactory,
            IMongoDatabase database,
            ILogger logger)
        {
            _handlerFactory = handlerFactory;
            _database = database;
            _logger = logger;
        }

        public async Task HandleAsync(RunBaggageReservationsMigration command)
        {
            await _handlerFactory
                .Create(command)
                .Run(async () =>
                {
                    _logger.Information($"Running {nameof(RunBaggageReservationsMigration)}");
                    var collection = _database.Reservations();

                    var reservationBuilder = Builders<Reservation>.Filter;
                    FieldDefinition<Reservation, IEnumerable<string>> baggageAllowancesField = "BaggageAllowances";
                    var filter = reservationBuilder.Exists(baggageAllowancesField, false);

                    var batchSize = 5;

                    var options = new FindOptions<Reservation>
                    {
                        BatchSize = batchSize
                    };

                    using (var cursor = await collection.FindAsync(filter, options))
                    {
                        var batch = 0;
                        while (await cursor.MoveNextAsync())
                        {
                            var reservations = cursor.Current.ToList();
                            var counter = 1;
                            foreach (var reservation in reservations)
                            {
                                reservation.BaggageAllowances = new Dictionary<string, Search.Shared.Models.BaggageAllowance>();
                                if (reservation.Legs != null)
                                {
                                    foreach (var legItem in reservation.Legs)
                                    {
                                        #pragma warning disable CS0612 // Type or member is obsolete
                                        reservation.BaggageAllowances[$"{legItem.Origin}-{legItem.Destination}"] = reservation.Baggage;
                                        #pragma warning restore CS0612 // Type or member is obsolete
                                    }
                                }

                                var update = Builders<Reservation>.Update
                                    .Set("BaggageAllowances", reservation.BaggageAllowances);

                                await collection.UpdateOneAsync(r => r.Id == reservation.Id, update);

                                _logger.Information("{Counter}/{Count} reservations have been updated", counter++,
                                    reservations.Count);

                                counter++;
                            }

                            batch++;
                        }
                    }
                })
                .OnError((ex) =>
                {
                    _logger.Error(ex, $"Error on updating reservation migration {nameof(RunBaggageReservationsMigration)}");
                })
                .ExecuteAsync();
        }
    }
}