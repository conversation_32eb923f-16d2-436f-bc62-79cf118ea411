using CTeleport.Common.Messaging.Services;
using CTeleport.Services.Booking.Commands;
using CTeleport.Services.Booking.Repositories.Queries;
using MongoDB.Driver;
using Serilog;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CTeleport.Services.Booking.Handlers
{
    public class BookingUpdatedAtHandler : ICommandHandler<BookingUpdatedAtMigration>
    {
        private readonly IHandlerFactory _handlerFactory;
        private readonly IMongoDatabase _database;
        private readonly ILogger _logger;

        public BookingUpdatedAtHandler(
            IHandlerFactory handlerFactory,
            IMongoDatabase database,
            ILogger logger)
        {
            _handlerFactory = handlerFactory;
            _database = database;
            _logger = logger;
        }

        public async Task HandleAsync(BookingUpdatedAtMigration command)
        {
            await _handlerFactory
                .Create(command)
                .Run(async () =>
                {
                    _logger.Information($"Running {nameof(BookingUpdatedAtMigration)}");
                    var collection = _database.Bookings();
                    var estimatedDocumentCountAsync = await collection.EstimatedDocumentCountAsync();

                    var batchSize = 10000;
                    var options = new FindOptions<Models.Booking>
                    {
                        BatchSize = batchSize
                    };

                    var filter = Builders<Models.Booking>.Filter.Eq(b => b.UpdatedAt, null);
                    using (var cursor = await collection.FindAsync(filter, options))
                    {
                        var counter = 0;
                        while (await cursor.MoveNextAsync())
                        {
                            var bookings = cursor.Current.ToList();
                            var bulkUpdateModel = new List<WriteModel<Models.Booking>>();
                            foreach (var booking in bookings)
                            {
                                var filterDefinition = Builders<Models.Booking>.Filter.Eq(b => b.Id, booking.Id);
                                var updateDefinition = Builders<Models.Booking>.Update.Set(b => b.UpdatedAt,
                                    booking.CancelledAt ?? booking.CreatedAt);

                                var upsertOne = new UpdateOneModel<Models.Booking>(filterDefinition, updateDefinition)
                                    { IsUpsert = true };
                                bulkUpdateModel.Add(upsertOne);
                            }

                            await collection.BulkWriteAsync(bulkUpdateModel);

                            counter += bookings.Count;
                            _logger.Information("{Counter}/{Count} bookings have been updated", counter++,
                                estimatedDocumentCountAsync);
                        }
                    }
                    
                    _logger.Information("All bookings have been updated");
                })
                .OnError((ex) =>
                {
                    _logger.Error(ex, $"Error on updating booking migration {nameof(BookingUpdatedAtMigration)}");
                })
                .ExecuteAsync();
        }
    }
}