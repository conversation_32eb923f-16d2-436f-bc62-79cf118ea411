using System.Collections.Generic;
using CTeleport.Messages.Commands.TradeCredit;
using CTeleport.Services.Booking.Enums;
using CTeleport.Services.Booking.Handlers.TradeCredit;
using CTeleport.Services.Booking.Models;
using CTeleport.Services.Booking.Repositories.Queries;
using MongoDB.Driver;
using Serilog;
using System.Linq;
using System.Threading.Tasks;
using CTeleport.Common.Messaging.Services;

namespace CTeleport.Services.Booking.Handlers
{
    public class CalcUninvoicedReservationsHandler : ICommandHandler<CalcUninvoicedReservations>
    {
        private readonly IMessageDispatcher _dispatcher;
        private readonly IHandlerFactory _handlerFactory;
        private readonly IMongoDatabase _database;
        private readonly ILogger _logger;

        private BookingState[] InactiveStates =>
            new[]
            {
                BookingState.Declined,
                BookingState.Cancelled
            };

        public CalcUninvoicedReservationsHandler(IMessageDispatcher dispatcher, IHandlerFactory handlerFactory, IMongoDatabase database, ILogger logger)
        {
            _dispatcher = dispatcher;
            _handlerFactory = handlerFactory;
            _database = database;
            _logger = logger;
        }

        public Task HandleAsync(CalcUninvoicedReservations command)
        {
            return _handlerFactory.Create(command)
                .Run(async () =>
                {
                    var uninvoicedReservations = await ReadUninvoicedReservations();
                    await _dispatcher.DispatchAsync(new UninvoicedReservationsCalculated
                    {
                        UninvoicedReservations = uninvoicedReservations
                    });
                })
                .OnSuccess(async () =>
                {
                    _logger.Information("CalcUninvoicedReservations successfully completed");
                })
                .OnError(async ex =>
                {
                    _logger.Error("CalcUninvoicedReservations failed, message: {Message}", ex.Message);
                })
                .ExecuteAsync();
        }

        private async Task<List<TenantUninvoicedReservations>> ReadUninvoicedReservations()
        {
            var builder = Builders<Reservation>.Filter;
            var uninvoicedReservations = await _database.ReservationsSecondary()
                .Aggregate()
                .Match(builder.And(
                    builder.Eq(r => r.State, ReservationState.Active),
                    builder.Eq(r => r.Ticketless, false),
                    builder.Exists($"{nameof(Reservation.Tickets)}.{nameof(Ticket.Number)}", false)))
                .Lookup<Reservation, Models.Booking, ReservationBookings>(
                    foreignCollection: _database.BookingsSecondary(),
                    localField: r => r.BookingId,
                    foreignField: b => b.Id,
                    @as: rb => rb.Bookings)
                .Match(rb => rb.Bookings.Any(booking => !InactiveStates.Contains(booking.State)
                                                        && booking.PaymentMethod == PaymentMethod.BankTransfer))
                .Group(g => g.TenantId, g => new TenantUninvoicedReservations
                {
                    TenantId = g.Key,
                    //have to do sum as double, as DB is configured to store decimal as double (facepalm)
                    TotalPrice = (decimal)g.Sum(rb => (double)rb.Price.Total)
                })
                .OutAsync("uninvoiced-reservations");
                
            return await uninvoicedReservations.ToListAsync();
        }
    }
}
