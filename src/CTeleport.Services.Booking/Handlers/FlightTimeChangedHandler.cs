using System.ComponentModel.DataAnnotations;
using System.Threading.Tasks;
using CTeleport.Common.Messaging.Services;
using CTeleport.Messages.Commands.BackOfficeNotifications;
using CTeleport.Messages.Events.Changes;
using Serilog;
using ServiceStack.Text;

namespace CTeleport.Services.Booking.Handlers;

public class FlightTimeChangedHandler : IEventHandler<FlightTimeChanged>
{
    private readonly IHandlerFactory _handlerFactory;
    private readonly ILogger _logger;
    private readonly IMessageDispatcher _dispatcher;
    
    public FlightTimeChangedHandler(IHandlerFactory handlerFactory, 
        ILogger logger, 
        IMessageDispatcher dispatcher)
    {
        _handlerFactory = handlerFactory;
        _logger = logger;
        _dispatcher = dispatcher;
    }
    
    public async Task HandleAsync(FlightTimeChanged @event)
    {
         await _handlerFactory
                .Create(@event)
                .Validate(() =>
                {
                    ValidateEvent(@event);
                    return Task.CompletedTask;
                })
                .Run(async () =>
                {
                    await _dispatcher.DispatchAsync(new SendNotification
                    {
                        Message = "Flight time has been changed",
                        Payload = @event.ToStringDictionary(),
                        NotificationSource = NotificationSource.FlightTimeChangedEvent,
                        NotificationType = NotificationType.Default
                    });
                })
                .OnError(ex =>
                {
                    _logger.Error(ex, "Error handling in FlightTimeChanged event. {Message}", ex.Message);
                    return Task.CompletedTask;
                })
                .ExecuteAsync();
    }

    private void ValidateEvent(FlightTimeChanged @event)
    {
        if (string.IsNullOrWhiteSpace(@event.ReservationId))
            throw new ValidationException("Reservation id is required.");

        if (string.IsNullOrWhiteSpace(@event.BookingId))
            throw new ValidationException("Booking id is required.");

        if (string.IsNullOrWhiteSpace(@event.FlightNumber))
            throw new ValidationException("Flight number is required.");

        if (string.IsNullOrWhiteSpace(@event.NewDepartureTime))
            throw new ValidationException("New departure time is required.");
                    
        if (string.IsNullOrWhiteSpace(@event.NewDepartureDate))
            throw new ValidationException("New departure date is required.");
    }
}