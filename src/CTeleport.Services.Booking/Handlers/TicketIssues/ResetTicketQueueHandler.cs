using System.Linq;
using System.Threading.Tasks;
using CTeleport.Common.Messaging.Services;
using CTeleport.Messages.Commands;
using CTeleport.Messages.Commands.Bookings;
using CTeleport.Messages.Commands.Tickets;
using CTeleport.Services.Booking.Models;
using CTeleport.Services.Booking.Services.Interfaces;
using Serilog;

namespace CTeleport.Services.Booking.Handlers;

public class ResetTicketQueueHandler : ICommandHandler<ResetTicketQueue>
{
    private readonly IHandlerFactory _handlerFactory;
    private readonly ITicketingQueueService _ticketingQueueService;
    private readonly IMessageDispatcher _dispatcher;
    private readonly ILogger _logger;

    public ResetTicketQueueHandler(IHandlerFactory handlerFactory, ITicketingQueueService ticketingQueueService, IMessageDispatcher dispatcher, ILogger logger)
    {
        _handlerFactory = handlerFactory;
        _ticketingQueueService = ticketingQueueService;
        _dispatcher = dispatcher;
        _logger = logger;
    }

    public Task HandleAsync(ResetTicketQueue command)
    {
        var handler = _handlerFactory.Create(command);

        return handler
            .Run(DispatchTicketingCommands)
            .Lock("Semaphore/ResetTicketQueue")
            .ExecuteAsync();
    }

    private async Task DispatchTicketingCommands()
    {
        var reservationsToIssue = await _ticketingQueueService.GetReservationsToIssue();

        _logger.Information("{Count} reservations to be ticketed", reservationsToIssue.Count);

        var enumerable = reservationsToIssue.Select(IssueReservation);
        await Task.WhenAll(enumerable);
    }

    private Task IssueReservation(Reservation reservation)
    {
        var command = new IssueTicketForReservation
        {
            Request = Request.New<IssueTicketForReservation>(), 
            ReservationId = reservation.Id
        };
            
        _logger.Information("Dispatching " + nameof(IssueTicketForReservation) + " for {ReservationId}", reservation.Id);
            
        return _dispatcher.DispatchAsync(command);
    }
}