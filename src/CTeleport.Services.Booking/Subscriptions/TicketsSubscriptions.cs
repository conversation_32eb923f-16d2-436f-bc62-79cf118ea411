using System.Threading;
using System.Threading.Tasks;
using CTeleport.Messages.Commands.Tickets;
using CTeleport.Messages.Events.Tickets;
using Autofac;
using CTeleport.Common.Messaging.Extensions;
using CTeleport.Messages.Commands.Changes;
using CTeleport.Services.Booking.Commands;
using RawRabbit;

namespace CTeleport.Services.Booking.Subscriptions
{
    internal static class TicketsSubscriptions
    {
        public static async Task Subscribe(ILifetimeScope scope, string queue, ushort basePrefetch, CancellationToken ct)
        {
            var bus = scope.Resolve<IBusClient>();

            await Task.WhenAll(
                bus.SubscribeToCommand<IssueTicket>(scope, ct, queue, basePrefetch),
                bus.SubscribeToCommand<IssueTicketForReservation>(scope, ct, queue, 1),
                bus.SubscribeToCommand<CheckTicketState>(scope, ct, queue, 1),
                bus.SubscribeToCommand<SetTicket>(scope, ct, queue, basePrefetch),
                bus.SubscribeToCommand<SetTicketAsRefunded>(scope, ct, queue, basePrefetch),
                bus.SubscribeToCommand<SetTicketAsUsed>(scope, ct, queue, basePrefetch),
                bus.SubscribeToCommand<SetTicketAsNoShow>(scope, ct, queue, basePrefetch),
                bus.SubscribeToCommand<SetTicketAsNonRefundable>(scope, ct, queue, basePrefetch),
                bus.SubscribeToCommand<SetProhibitAutoRefund>(scope, ct, queue, basePrefetch),
                bus.SubscribeToCommand<TicketIssuedUpdateRemarks>(scope, ct, queue, basePrefetch),
                bus.SubscribeToCommand<SyncChangedTicket>(scope, ct, queue, basePrefetch),

                bus.SubscribeToCommand<ResendTicketIssued>(scope, ct, queue, basePrefetch),
                bus.SubscribeToCommand<ResendTicketVoided>(scope, ct, queue, basePrefetch),
                bus.SubscribeToCommand<ResendTicketRefunded>(scope, ct, queue, basePrefetch),

                bus.SubscribeToEvent<ProviderIssuedTicket>(scope, ct, queue, basePrefetch, retryOptions: RetryOptions.Default),

                bus.SubscribeToEvent<ProviderIssueTicketRejected>(scope, ct, queue, basePrefetch, retryOptions: RetryOptions.Default),
                bus.SubscribeToEvent<ProviderRefundTicketRejected>(scope, ct, queue, basePrefetch, retryOptions: RetryOptions.Default),

                bus.SubscribeToEvent<TicketIssued>(scope, ct, queue, basePrefetch),
                bus.SubscribeToEvent<IssueTicketRejected>(scope, ct, queue, basePrefetch),
                bus.SubscribeToEvent<TicketSetAsNoShow>(scope, ct, queue, basePrefetch)
            );
        }
    }
}
