using System.Runtime.Serialization;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

namespace CTeleport.Services.Booking.Enums;

/// <summary>Fare change reason</summary>
[JsonConverter(typeof (StringEnumConverter))]
public enum FareChangeReason
{
    [EnumMember(Value = "reissue")] Reissue,
    [EnumMember(Value = "reissue-manual")] ReissueManual,
    [EnumMember(Value = "class-drop")] ClassDrop,
    [EnumMember(Value = "fare-update")] FareUpdate,
}