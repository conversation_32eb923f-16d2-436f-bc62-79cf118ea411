using Polly;
using System;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;

namespace CTeleport.Services.CustomFields.HttpHandlers;

// TODO: move to shared solution (NuGet package)
internal class PolicyHttpHandler : DelegatingHandler
{
    private static readonly HttpRequestOptionsKey<HttpResponseMessage> _priorResponseKey = new("PolicyHttpHandler.PriorResponse");
    private readonly IAsyncPolicy _policy;

    public PolicyHttpHandler(IAsyncPolicy policy, HttpMessageHandler innerHandler = null)
        : base(innerHandler ?? new HttpClientHandler())
    {
        _policy = policy;
    }

    protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
    {
        if (request == null)
        {
            throw new ArgumentNullException(nameof(request));
        }

        return await _policy.ExecuteAsync(async () => await SendCoreAsync(request, cancellationToken));
    }

    private async Task<HttpResponseMessage> SendCoreAsync(HttpRequestMessage request, CancellationToken cancellationToken)
    {
        if (request.Options.TryGetValue(_priorResponseKey, out var priorResult))
        {
            priorResult.Dispose();
        }

        var result = await base.SendAsync(request, cancellationToken);

        request.Options.Set(_priorResponseKey, result);

        return result;
    }
}