using System;
using System.Collections.Generic;

namespace CTeleport.Services.CustomFields.Dto.CustomFieldsClient;

//public class TenantCustomFieldsGroupDto
//{
//    public string Id { get; set; }

//    public string TenantWildcard { get; set; }

//    public DateTime CreatedAt { get; set; }

//    public DateTime? UpdatedAt { get; set; }

//    public TenantCustomBasicFieldsGroupDto BasicFields { get; set; } = new TenantCustomBasicFieldsGroupDto();

//    public IReadOnlyCollection<TenantCustomFieldDto> CustomFields { get; set; } = new List<TenantCustomFieldDto>();
//}
