using CTeleport.Services.CustomFields.Configuration;
using CTeleport.Services.CustomFields.Handlers.Models;
using CTeleport.Services.CustomFields.Models;
using CTeleport.Services.CustomFields.Services.Interfaces;
using MongoDB.Driver;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CTeleport.Services.CustomFields.Services;

public class CustomFieldsMigrationService : ICustomFieldsMigrationService
{
    private readonly MongoClient _mongoClient;
    private readonly CustomFieldsMigrationOptions _options;

    private readonly ILogger _logger;

    public CustomFieldsMigrationService(
        MongoClient mongoClient,
        CustomFieldsMigrationOptions options,
        ILogger logger)
    {
        _mongoClient = mongoClient;
        _options = options;
        _logger = logger;
    }

    public async Task MigrateAsync()
    {
        var originalCollection = _mongoClient
            .GetDatabase(_options.OriginalDatabaseName)
            .GetCollection<TenantCustomFields>(_options.OriginalCollectionName);

        var destinationModels = new List<CustomFieldsGroupMigrationModel>();

        await foreach (var customFieldGroup in GetAsyncEnumerable(originalCollection))
        {
            if (customFieldGroup is null)
            {
                continue;
            }

            var destinationModel = MapToDestinationModel(customFieldGroup);
            destinationModels.Add(destinationModel);
        }

        await _mongoClient
            .GetDatabase(_options.DestinationDatabaseName)
            .GetCollection<CustomFieldsGroupMigrationModel>(_options.DestinationCollectionName)
            .InsertManyAsync(destinationModels);
    }

    public async Task RevertAsync()
    {
        await _mongoClient
            .GetDatabase(_options.DestinationDatabaseName)
            .DropCollectionAsync(_options.DestinationCollectionName);
    }

    private static async IAsyncEnumerable<T> GetAsyncEnumerable<T>(IMongoCollection<T> collection)
    {
        using var cursor = await collection.Find(_ => true).ToCursorAsync();

        while (await cursor.MoveNextAsync())
        {
            foreach (var entity in cursor.Current)
            {
                yield return entity;
            }
        }
    }

    public CustomFieldsGroupMigrationModel MapToDestinationModel(TenantCustomFields original)
    {
        return new CustomFieldsGroupMigrationModel
        {
            Id = original.Id,
            TenantWildcard = original.Tenant,
            CreatedAt = original.CreatedAt,
            UpdatedAt = original.UpdatedAt,
            BasicFields = new BasicFieldsMigrationModel
            {
                CommentFieldIsVisible = original.ShowCommentField,
                ContactsFieldsIsVisible = original.ShowContactsFields
            },
            CustomFields = original.CustomFields?
                .Where(f => f != null)
                .Select(f => MapCustomField(f, original))
                .ToArray()
                ?? Array.Empty<CustomFieldMigrationModel>()
        };
    }

    public CustomFieldMigrationModel MapCustomField(CustomField field, TenantCustomFields original)
    {
        const TenantCustomFieldType defaultFieldType = TenantCustomFieldType.Text;

        if (field.FieldType is null)
        {
            _logger
                .ForContext(nameof(TenantCustomFields.Id), original.Id)
                .Warning($"CustomFieldsGroup {original.Id} has custom field {field.FieldName} with empty field type, default value {defaultFieldType} is used");
        }

        return new CustomFieldMigrationModel
        {
            FieldName = field.FieldName,
            FieldType = field.FieldType ?? defaultFieldType,
            Name = field.Name,
            Placeholder = field.Placeholder,
            Tooltip = field.Tooltip,
            IsAvailableExcel = field.ExportToExcel,
            IsRequired = field.Validation?.IsRequired ?? false,
            Validation = MapValidation(field.Validation),
            Options = MapOptions(field.Options?.ToArray())
        };
    }

    public CustomFieldValidationMigrationModel MapValidation(CustomFieldValidation validation)
    {
        if (validation is null || validation.RegularExpression is null)
        {
            return null;
        }

        return new CustomFieldValidationMigrationModel
        {
            ErrorMessage = validation.RegularExpression.ErrorMessage,
            Pattern = validation.RegularExpression.Pattern
        };
    }

    public static CustomOptionMigrationModel[] MapOptions(IReadOnlyCollection<CustomOption> options)
    {
        if (options is null || !options.Any())
        {
            return null;
        }

        return options.Select(option => new CustomOptionMigrationModel
        {
            Code = option.Code,
            Key = option.Key,
            Value = option.Value,
            IsHidden = option.IsHidden,
        }).ToArray();
    }
}
