using System.Collections.Generic;
using SmartFormat.Core.Extensions;

namespace CTeleport.Services.CustomFields.Helpers;

public class FallbackFormatter : IFormatter
{
    // Formatter name as used in the template: {something:fallback:...}
    public string[] Names { get; set; } = { "fallback" };

    public bool TryEvaluateFormat(IFormattingInfo formattingInfo)
    {
        // Read the raw text inside the formatter, e.g. cost_centre|Vessel.CostCentre
        var raw = formattingInfo.Format?.RawText;
        if (string.IsNullOrWhiteSpace(raw))
            return false;

        // Split into primary key and fallback key
        var parts = raw.Split('|');
        if (parts.Length != 2)
            return false;

        var primaryKey = parts[0].Trim();   // e.g. "cost_centre"
        var fallbackPath = parts[1].Trim(); // e.g. "Vessel.CostCentre"

        // Try to resolve the primary key from the current value (e.g. "Custom")
        if (formattingInfo.CurrentValue is IDictionary<string, string> dict &&
            dict.TryGetValue(primaryKey, out var primaryValue) &&
            !string.IsNullOrWhiteSpace(primaryValue))
        {
            // If value is present and not empty, use it
            formattingInfo.Write(primaryValue);
            return true;
        }

        // Try to resolve fallback by formatting the fallback path using the full argument object
        try
        {
            var fallbackFormatted = formattingInfo.FormatDetails.Formatter.Format(
                $"{{{fallbackPath}}}", // Format: {Vessel.CostCentre}
                formattingInfo.FormatDetails.OriginalArgs
            );

            if (!string.IsNullOrWhiteSpace(fallbackFormatted))
            {
                formattingInfo.Write(fallbackFormatted);
                return true;
            }
        }
        catch
        {
            // Ignore fallback errors — return empty if nothing found
        }

        // If both primary and fallback fail, return empty
        return true;
    }
}