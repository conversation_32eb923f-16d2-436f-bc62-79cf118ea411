using System;
using CTeleport.Messages.Commands.Models;
using CTeleport.Services.CustomFields.Models;

namespace CTeleport.Services.CustomFields.Helpers
{
    public static class RemarkTypeHelper
    {
        public static RemarkType ToRemarkType(CustomFieldType fieldType)
        {
            switch (fieldType)
            {
                case CustomFieldType.SSR:
                    return RemarkType.SSR;
                case CustomFieldType.OSI:
                    return RemarkType.OSI;
                case CustomFieldType.General:
                    return RemarkType.General;
                case CustomFieldType.Accounting:
                    return RemarkType.Accounting;
                case CustomFieldType.Custom:
                    return RemarkType.Custom;
                case CustomFieldType.Name:
                    return RemarkType.Name;
                default:
                    throw new ArgumentOutOfRangeException(nameof(fieldType), fieldType, "Custom field type not supported");
            }
        }
    }
}