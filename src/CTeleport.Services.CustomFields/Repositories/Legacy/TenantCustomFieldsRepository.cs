using CTeleport.Common.Mongo;
using CTeleport.Services.CustomFields.Helpers;
using CTeleport.Services.CustomFields.Models.Legacy;
using CTeleport.Services.CustomFields.Repositories.Interfaces.Legacy;
using MongoDB.Bson;
using MongoDB.Driver;
using MongoDB.Driver.Linq;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;

namespace CTeleport.Services.CustomFields.Repositories.Legacy;

public class TenantCustomFieldsRepository : SafeRepositoryBase, ITenantCustomFieldsRepository
{
    public TenantCustomFieldsRepository(IMongoDatabase database, ILogger logger) : base(database, logger) { }

    public async Task<List<TenantCustomFields>> GetAsync(string tenantId, CancellationToken cancellationToken = default)
    {
        var rs = await SafeReadAsync(Database.TenantCustomFields().AsQueryable()
            .Where(x => TenantHelper.GetCodes(tenantId).Contains(x.Tenant))
            .ToListAsync(cancellationToken));
        return rs;
    }

    public async Task<List<TenantCustomFields>> GetByTenantIdAsync(string tenantId, CancellationToken cancellationToken = default)
    {
        var rs = await SafeReadAsync(Database.TenantCustomFields().AsQueryable()
            .Where(f => f.Tenant == tenantId)
            .ToListAsync(cancellationToken));
        return rs;
    }

    public async Task AddAsync(TenantCustomFields item, CancellationToken cancellationToken = default)
    {
        if (item is null)
        {
            throw new ArgumentNullException(nameof(item));
        }

        item.CreatedAt = DateTime.UtcNow;

        await Database.TenantCustomFields().InsertOneAsync(item, new InsertOneOptions(), cancellationToken);
    }

    public async Task<TenantCustomFieldsPage> GetAllByFilterAsync(
        FilterDefinition<TenantCustomFields> securityFilters,
        string filterByTenantId,
        int limit,
        int offset,
        Order? orderByTenantId = null,
        CancellationToken cancellationToken = default)
    {
        var builder = Builders<TenantCustomFields>.Filter;
        var filter = securityFilters;

        if (!string.IsNullOrWhiteSpace(filterByTenantId))
        {
            filter &= builder.Regex(t => t.Tenant, BsonRegularExpression.Create(new Regex($".*{filterByTenantId}.*",
                RegexOptions.IgnoreCase | RegexOptions.Singleline)));
        }

        var totalCount = await Database.TenantCustomFields().CountDocumentsAsync(filter, cancellationToken: cancellationToken);

        var sort = orderByTenantId is null
            ? Builders<TenantCustomFields>.Sort.Descending(t => t.CreatedAt)
            : orderByTenantId == Order.Asc
                ? Builders<TenantCustomFields>.Sort.Ascending(t => t.Tenant)
                : Builders<TenantCustomFields>.Sort.Descending(t => t.Tenant);

        var items = await Database.TenantCustomFields()
            .Find(filter, new FindOptions
            {
                Collation = new Collation("en")
            })
            .Sort(sort)
            .Skip(offset)
            .Limit(limit)
            .ToListAsync(cancellationToken);

        return new TenantCustomFieldsPage
        {
            Items = items,
            Offset = offset,
            Limit = limit,
            TotalCount = totalCount
        };
    }

    public Task<TenantCustomFields> GetByIdAsync(string id, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(id))
        {
            throw new ArgumentNullException(nameof(id));
        }

        return Database.TenantCustomFields().AsQueryable().FirstOrDefaultAsync(f => f.Id == id, cancellationToken);
    }

    public Task UpdateAsync(TenantCustomFields tenantCustomFields, CancellationToken cancellationToken = default)
    {
        if (tenantCustomFields is null)
        {
            throw new ArgumentNullException(nameof(tenantCustomFields));
        }
        if (string.IsNullOrWhiteSpace(tenantCustomFields.Id))
        {
            throw new ArgumentNullException(nameof(TenantCustomFields.Id));
        }

        tenantCustomFields.UpdatedAt = DateTime.UtcNow;

        return Database.TenantCustomFields().ReplaceOneAsync(t => t.Id == tenantCustomFields.Id, tenantCustomFields, new ReplaceOptions
        {
            IsUpsert = false
        }, cancellationToken);
    }
}