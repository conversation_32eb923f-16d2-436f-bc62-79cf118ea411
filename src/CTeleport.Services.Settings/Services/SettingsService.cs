using CTeleport.Services.Settings.Clients;
using CTeleport.Services.Settings.Contracts.TenantManagementService;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace CTeleport.Services.Settings.Services
{
    public class SettingsService : ISettingsService
    {
        private readonly IAirProvidersService _providersService;
        private readonly ITenantManagementServiceClient _tenantManagementServiceClient;

        public SettingsService(
            IAirProvidersService providersService,
            ITenantManagementServiceClient tenantManagementServiceClient)
        {
            _providersService = providersService;
            _tenantManagementServiceClient = tenantManagementServiceClient;
        }

        public async Task<Tenant> GetTenantAsync(string tenantId, bool withCache = true)
        {
            return await _tenantManagementServiceClient.GetTenantAsync(tenantId, withCache);
        }

        public async Task<Dictionary<string, string>> GetTenantCorporateCodesAsync(string tenantId)
        {
            var tenant = await GetTenantAsync(tenantId);

            return tenant?.CorporateCodes ?? new Dictionary<string, string>();
        }

        public async Task<bool> CheckSourceGloballyEnabled(string source)
        {
            return await _providersService.CheckSourceGloballyEnabled(source);
        }
    }
}