using CTeleport.Services.Settings.Contracts.TenantManagementService;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace CTeleport.Services.Settings.Services
{
    public interface ISettingsService
    {
        /// <summary>
        /// Get Tenant by tenant id.
        /// </summary>
        /// <param name="tenantId">Tenant Id</param>
        /// <param name="withCache">Look up cache for tenant data</param>
        /// <returns>Tenant Object</returns>
        Task<Tenant> GetTenantAsync(string tenantId, bool withCache = true);

        /// <summary>
        /// Get corporate codes for specified tenant
        /// </summary>
        /// <param name="tenantId"></param>
        /// <returns></returns>
        Task<Dictionary<string, string>> GetTenantCorporateCodesAsync(string tenantId);

        /// <summary>
        /// Check source if it is enabled globally.
        /// </summary>
        /// <param name="source"></param>
        /// <returns></returns>
        Task<bool> CheckSourceGloballyEnabled(string source);
    }
}