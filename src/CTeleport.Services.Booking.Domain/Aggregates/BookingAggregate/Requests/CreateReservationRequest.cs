using CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.DomainEvents;

namespace CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Requests;

public record CreateReservationRequest
{
    public string ReservationId { get; init; } = Unknown;
    public string Locator { get; init; } = Unknown;

    public ReservationCreated ToEvent(Booking aggregate)
        => new(aggregate)
        {
            ReservationId = ReservationId,
            Locator = Locator
        };
}