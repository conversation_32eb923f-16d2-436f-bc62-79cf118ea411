using System.Linq.Expressions;
using CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Entities;
using CTeleport.Services.Booking.Domain.SeedWork;

namespace CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Specifications;

public class ReservationPresenceSpec : Specification<IReadOnlyCollection<ReservationEntity>>
{
    private readonly string _reservationId;

    public ReservationPresenceSpec(string reservationId)
        => _reservationId = reservationId;

    protected internal override string ErrorMessage => "Reservation is already present";

    protected internal override Expression<Func<IReadOnlyCollection<ReservationEntity>, bool>> ToExpression()
        => rc => !rc.Any(res => res.Id.Equals(_reservationId));
}