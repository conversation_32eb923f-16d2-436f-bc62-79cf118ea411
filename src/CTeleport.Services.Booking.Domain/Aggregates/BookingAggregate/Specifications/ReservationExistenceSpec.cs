using System.Linq.Expressions;
using CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Entities;
using CTeleport.Services.Booking.Domain.SeedWork;

namespace CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Specifications;

public class ReservationExistenceSpec : Specification<IReadOnlyCollection<ReservationEntity>>
{
    private readonly string _reservationId;

    public ReservationExistenceSpec(string reservationId)
        => _reservationId = reservationId;

    protected internal override string ErrorMessage => "Reservation not found";

    protected internal override Expression<Func<IReadOnlyCollection<ReservationEntity>, bool>> ToExpression()
        => rc => rc.Count(res => res.Id.Equals(_reservationId)) == 1;
}