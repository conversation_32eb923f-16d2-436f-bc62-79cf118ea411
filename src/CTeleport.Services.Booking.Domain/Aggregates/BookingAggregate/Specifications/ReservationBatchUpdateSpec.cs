using System.Linq.Expressions;
using CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Entities;
using CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Enums;
using CTeleport.Services.Booking.Domain.SeedWork;

namespace CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Specifications;

public class ReservationBatchUpdateSpec : Specification<IReadOnlyCollection<ReservationEntity>>
{
    protected internal override string ErrorMessage => "Update failed: one or more reservations cannot be updated";

    protected internal override Expression<Func<IReadOnlyCollection<ReservationEntity>, bool>> ToExpression()
        => rc => rc.All(r => !r.Id.Equals(Unknown) &&
                             !r.ProviderKey.Equals(Unknown) &&
                             !r.ProviderType.Equals(ProviderType.Unknown));
}