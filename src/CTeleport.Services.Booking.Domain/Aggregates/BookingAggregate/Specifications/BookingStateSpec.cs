using System.Linq.Expressions;
using CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Entities;
using CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Enums;
using CTeleport.Services.Booking.Domain.SeedWork;

namespace CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Specifications;

public class BookingStateSpec : Specification<BookingEntity>
{
    private readonly BookingState[] _states;

    public BookingStateSpec(params BookingState[] states)
        => _states = states;

    private string States => string.Join(InnerStringSeparator, _states);

    protected internal override string ErrorMessage => $"Invalid booking state, expected: {States}";

    protected internal override Expression<Func<BookingEntity, bool>> ToExpression()
        => b => _states.Contains(b.State);
}