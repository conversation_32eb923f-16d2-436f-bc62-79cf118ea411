using CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.IntegrationEvents;
using MediatR;

namespace CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Commands;

public record CreateBooking : IRequest
{
    public string Id { get; init; } = Unknown;
    public string TenantId { get; init; } = Unknown;
}

public class CreateBookingHandler : IRequestHandler<CreateBooking>
{
    private readonly IBookingRepository _bookingRepository;
    private readonly IPublisher _publisher;

    public CreateBookingHandler(IBookingRepository bookingRepository, IPublisher publisher)
    {
        _bookingRepository = bookingRepository;
        _publisher = publisher;
    }

    public async Task Handle(CreateBooking command, CancellationToken cancellationToken)
    {
        var booking = new Booking(command.Id, command.TenantId);

        await _bookingRepository.SaveAsync(booking);

        var @event = new BookingCreated
        {
            EntityId = booking.Id,
            EventId = Guid.NewGuid()
        };

        // TODO: dispatch event using custom events producer
        await _publisher.Publish(@event, cancellationToken);
    }
}