namespace CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.DomainEvents;

/// <summary>
/// This event is triggered when the reservation was detached from the booking
/// </summary>
public record ReservationRemoved : BookingAggregateEvent
{
    public ReservationRemoved(string id, long version, long timestamp)
        : base(id, version, timestamp)
    {
    }

    public ReservationRemoved(Booking aggregate)
        : base(aggregate)
    {
    }

    public string ReservationId { get; init; } = Unknown;
}