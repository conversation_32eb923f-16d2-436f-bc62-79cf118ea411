using CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Enums;

namespace CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.DomainEvents;

/// <summary>
/// This event is triggered when a reservation is attached to the booking
/// </summary>
public record ReservationAdded : BookingAggregateEvent
{
    public ReservationAdded(string id, long version, long timestamp)
        : base(id, version, timestamp)
    {
    }

    public ReservationAdded(Booking aggregate)
        : base(aggregate)
    {
    }

    public string ReservationId { get; init; } = Unknown;
    public string ProviderKey { get; init; } = Unknown;
    public ProviderType ProviderType { get; init; } = ProviderType.Unknown;
}