using CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Enums;

namespace CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.DomainEvents;

/// <summary>
/// This event is triggered when the booking saga payment state is changed
/// </summary>
/// <remarks>For backward compatibility. To be removed.</remarks>
public record BookingSagaPaymentStateChanged : BookingAggregateEvent
{
    public BookingSagaPaymentStateChanged(string id, long version, long timestamp)
        : base(id, version, timestamp)
    {
    }

    public BookingSagaPaymentStateChanged(Booking aggregate)
        : base(aggregate)
    {
    }

    public BookingSagaPaymentState BookingSagaPaymentState { get; init; }
}