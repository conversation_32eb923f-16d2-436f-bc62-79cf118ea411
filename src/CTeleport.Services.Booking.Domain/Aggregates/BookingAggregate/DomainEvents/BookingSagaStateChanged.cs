using CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Enums;

namespace CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.DomainEvents;

/// <summary>
/// This event is triggered when the booking saga state is changed
/// </summary>
/// <remarks>For backward compatibility. To be removed.</remarks>
public record BookingSagaStateChanged : BookingAggregateEvent
{
    public BookingSagaStateChanged(string id, long version, long timestamp)
        : base(id, version, timestamp)
    {
    }

    public BookingSagaStateChanged(Booking aggregate)
        : base(aggregate)
    {
    }

    public BookingSagaState BookingSagaState { get; init; }
}