using CTeleport.Services.Booking.Domain.SeedWork;
using FareTypeExt = CTeleport.Services.Search.Shared.Enums.FareType;

namespace CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Enums;

public class FareType : Enumeration
{
    public static readonly FareType Unknown = new(0, Globals.Unknown.ToLowerInvariant());
    public static readonly FareType Public = new(1, "public");
    public static readonly FareType Marine = new(2, "marine");
    public static readonly FareType Mixed = new(3, "mixed");
    public static readonly FareType Aircrew = new(4, "aircrew");
    public static readonly FareType Energy = new(5, "energy");
    public static readonly FareType Private = new(6, "private");

    public FareType(int value, string name)
        : base(value, name)
    {
    }

    public static FareType ToFareType(FareTypeExt fareType)
        => fareType switch
        {
            FareTypeExt.Public => Public,
            FareTypeExt.Marine => Marine,
            FareTypeExt.Mixed => Mixed,
            FareTypeExt.Aircrew => Aircrew,
            FareTypeExt.Energy => Energy,
            FareTypeExt.Private => Private,
            _ => Unknown
        };

    public static FareTypeExt ToFareTypeExt(FareType fareType)
        => fareType switch
        {
            _ when fareType.Equals(Public) => FareTypeExt.Public,
            _ when fareType.Equals(Marine) => FareTypeExt.Marine,
            _ when fareType.Equals(Mixed) => FareTypeExt.Mixed,
            _ when fareType.Equals(Aircrew) => FareTypeExt.Aircrew,
            _ when fareType.Equals(Energy) => FareTypeExt.Energy,
            _ when fareType.Equals(Private) => FareTypeExt.Private,
            _ => FareTypeExt.Public
        };
}