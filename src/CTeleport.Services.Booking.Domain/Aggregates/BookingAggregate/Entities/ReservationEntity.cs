using CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Enums;

namespace CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Entities;

public record ReservationEntity : IComparable<ReservationEntity>
{
    public string Id { get; init; } = Unknown;
    public string ProviderKey { get; init; } = Unknown;
    public string? Locator { get; init; }
    public string? OriginalReservationId { get; init; }
    public string? OriginalProviderKey { get; init; }
    public string? OriginalLocator { get; init; }
    public bool IsVirtualized { get; init; }
    public bool IsRemoved { get; init; }
    public ReservationState State { get; init; }
    public PriceEntity Price { get; init; } = new();
    public TermsEntity Terms { get; init; } = new();
    public ProviderItineraryEntity ProviderItinerary { get; init; } = new();
    public ProviderType ProviderType { get; init; } = ProviderType.Unknown;
    public IReadOnlyCollection<AncillaryEntity> Ancillaries { get; init; } = Array.Empty<AncillaryEntity>();

    public ReservationEntity ChangeState(ReservationState state)
        => this with
        {
            State = state,
            Price = PriceEntity.New(Price),
            Terms = TermsEntity.New(Terms),
            ProviderItinerary = ProviderItineraryEntity.New(ProviderItinerary),
            Ancillaries = Ancillaries.Select(AncillaryEntity.New).ToImmutableList()
        };

    public ReservationEntity ChangeProviderKey(string providerKey)
        => this with
        {
            ProviderKey = providerKey,
            OriginalProviderKey = ProviderKey,
            Price = PriceEntity.New(Price),
            Terms = TermsEntity.New(Terms),
            ProviderItinerary = ProviderItineraryEntity.New(ProviderItinerary),
            Ancillaries = Ancillaries.Select(AncillaryEntity.New).ToImmutableList()
        };

    public ReservationEntity ChangeLocator(string locator)
        => this with
        {
            Locator = locator,
            Price = PriceEntity.New(Price),
            Terms = TermsEntity.New(Terms),
            ProviderItinerary = ProviderItineraryEntity.New(ProviderItinerary),
            Ancillaries = Ancillaries.Select(AncillaryEntity.New).ToImmutableList()
        };

    public virtual bool Equals(ReservationEntity? other)
    {
        if (other is null)
            return false;

        return ReferenceEquals(this, other) || Id.Equals(other.Id);
    }

    public int CompareTo(ReservationEntity? other)
    {
        if (other is null)
            return 1;

        if (Equals(other))
            return 0;

        if (other.ProviderItinerary.DepartureTimestampUtc is null)
            return 1;

        if (!ProviderItinerary.DepartureTimestampUtc.HasValue)
            return -1;

        return ProviderItinerary.DepartureTimestampUtc.Value.CompareTo(other.ProviderItinerary.DepartureTimestampUtc.Value);
    }

    public override int GetHashCode()
        => HashCode.Combine(Id);
}