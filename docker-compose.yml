version: '2'

services:
  
   mongo:
     image: mongo:4.4.5
     volumes:
       - mongodata:/data/db
     restart: unless-stopped
     ports:
       - "127.0.0.1:27017:27017"

   rabbitmq:
     image: rabbitmq:3.8.18-management-alpine
     volumes:
       - rabbitmqdata:/var/lib/rabbitmq
     restart: unless-stopped
     hostname: rabbitmq
     ports:
       - "127.0.0.1:5672:5672"
       - "127.0.0.1:15672:15672"

   redis:
     image: redis:6.0.9-alpine
     restart: unless-stopped
     hostname: redis
     ports:
       - "127.0.0.1:6379:6379"

volumes:
    mongodata:
    rabbitmqdata: