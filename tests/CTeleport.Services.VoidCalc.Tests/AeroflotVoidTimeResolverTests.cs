using CTeleport.Services.VoidCalc.Configuration;
using CTeleport.Services.VoidCalc.Services;
using CTeleport.Services.VoidCalc.Services.Carriers;
using NodaTime;

namespace CTeleport.Services.VoidCalc.Tests;

public class AeroflotVoidTimeResolverTests
{
    private readonly AeroflotVoidTimeResolver _service;
    private readonly DateTimeZone _pccTimeZone;

    public AeroflotVoidTimeResolverTests()
    {
        _pccTimeZone = DateTimeZoneProviders.Tzdb["Europe/Riga"];
        
        var options = new VoidOptions
        {
            VoidSafetyTime = 60,
            CustomVoidRules = new Dictionary<string, VoidRule>
            {
                { "1G.55EL", VoidRule.SameDayMidnight }
            }
        };
        
        _service = new AeroflotVoidTimeResolver(new VoidTimeCalculator(options));
        
    }

    [Fact]
    public void CalcLastVoidTimeAeroflot_Returns_1H_After_Issuing_Departure_Edge()
    {
        var issueAt = new DateTime(2017, 11, 5, 7, 59, 59, DateTimeKind.Utc);
        var departureAt = new DateTime(2017, 11, 5, 10, 0, 0, DateTimeKind.Utc);
        
        var lastVoidAt = _service.Calculate(VoidRule.SameDayMidnight, _pccTimeZone, issueAt, departureAt);

        Assert.Equal(issueAt.Year, lastVoidAt.Year);
        Assert.Equal(issueAt.Month, lastVoidAt.Month);
        Assert.Equal(issueAt.Day, lastVoidAt.Day);
        Assert.Equal(8, lastVoidAt.Hour);
        Assert.Equal(59, lastVoidAt.Minute);
        Assert.Equal(59, lastVoidAt.Second);
    }

    [Fact]
    public void CalcLastVoidTimeAeroflot_Returns_1H_Before_Departure()
    {
        var issueAt = new DateTime(2017, 11, 5, 8, 0, 1, DateTimeKind.Utc);
        var departureAt = new DateTime(2017, 11, 5, 10, 0, 0, DateTimeKind.Utc);

        var lastVoidAt = _service.Calculate(VoidRule.SameDayMidnight, _pccTimeZone, issueAt, departureAt);

        Assert.Equal(issueAt.Year, lastVoidAt.Year);
        Assert.Equal(issueAt.Month, lastVoidAt.Month);
        Assert.Equal(issueAt.Day, lastVoidAt.Day);
        Assert.Equal(9, lastVoidAt.Hour);
        Assert.Equal(0, lastVoidAt.Minute);
        Assert.Equal(0, lastVoidAt.Second); // Not 1
    }

    [Fact]
    public void CalcLastVoidTimeAeroflot_Returns_1H_After_Issuing_Midnight_Edge()
    {
        var issueAt = new DateTime(2017, 11, 4, 19, 59, 59, DateTimeKind.Utc);
        var departureAt = new DateTime(2017, 11, 5, 10, 0, 0, DateTimeKind.Utc);

        var lastVoidAt = _service.Calculate(VoidRule.SameDayMidnight, _pccTimeZone, issueAt, departureAt);

        Assert.Equal(issueAt.Year, lastVoidAt.Year);
        Assert.Equal(issueAt.Month, lastVoidAt.Month);
        Assert.Equal(issueAt.Day, lastVoidAt.Day);
        Assert.Equal(20, lastVoidAt.Hour);
        Assert.Equal(59, lastVoidAt.Minute);
        Assert.Equal(59, lastVoidAt.Second);
    }

    [Fact]
    public void CalcLastVoidTimeAeroflot_Returns_1H_Before_Midnight()
    {
        var issueAt = new DateTime(2017, 11, 4, 20, 0, 1, DateTimeKind.Utc);
        var departureAt = new DateTime(2017, 11, 5, 10, 0, 0, DateTimeKind.Utc);

        var lastVoidAt = _service.Calculate(VoidRule.SameDayMidnight, _pccTimeZone, issueAt, departureAt);

        Assert.Equal(issueAt.Year, lastVoidAt.Year);
        Assert.Equal(issueAt.Month, lastVoidAt.Month);
        Assert.Equal(issueAt.Day, lastVoidAt.Day);
        Assert.Equal(21, lastVoidAt.Hour);
        Assert.Equal(0, lastVoidAt.Minute);
        Assert.Equal(0, lastVoidAt.Second); // Not 1
    }

    [Fact]
    public void CalcLastVoidTimeAeroflot_Returns_1H_Before_Midnight_Outside_Special_Timeframe()
    {
        var issueAt = new DateTime(2017, 11, 3, 12, 0, 0, DateTimeKind.Utc);
        var departureAt = new DateTime(2017, 11, 5, 10, 0, 0, DateTimeKind.Utc);

        var lastVoidAt = _service.Calculate(VoidRule.SameDayMidnight, _pccTimeZone, issueAt, departureAt);

        Assert.Equal(issueAt.Year, lastVoidAt.Year);
        Assert.Equal(issueAt.Month, lastVoidAt.Month);
        Assert.Equal(issueAt.Day, lastVoidAt.Day);
        Assert.Equal(21, lastVoidAt.Hour);
        Assert.Equal(0, lastVoidAt.Minute);
        Assert.Equal(0, lastVoidAt.Second);
    }
}