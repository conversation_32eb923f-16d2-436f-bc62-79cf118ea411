using CTeleport.Services.VoidCalc.Services.Carriers;

namespace CTeleport.Services.VoidCalc.Tests;

public class IssueDateTimeAsVoidTimeResolverTests
{
    private readonly IssueDateTimeAsVoidTimeResolver _target;

    public IssueDateTimeAsVoidTimeResolverTests()
    {
        _target = new IssueDateTimeAsVoidTimeResolver();
    }
    
    [Fact]
    public void IsApplicable_AirGatewayAegeanAirlines_False()
    {
        var actual = _target.IsApplicable("A3", new HashSet<string>(0), "AG.SOURCE");
        
        Assert.False(actual);
    }
    
    [Fact]
    public void IsApplicable_TravelportAegeanAirlines_True()
    {
        var actual = _target.IsApplicable("A3", new HashSet<string>(0), "1G.0000");
        
        Assert.True(actual);
    }
    
    [Theory]
    [InlineData("UK")]
    [InlineData("GP")]
    [InlineData("HB")]
    [InlineData("VB")]
    public void IsApplicable_AirGatewayUK_True(string carrier)
    {
        var actual = _target.IsApplicable(carrier, new HashSet<string>(0), "AG.SOURCE");
        
        Assert.True(actual);
    }
    
    [Theory]
    [InlineData("UK")]
    [InlineData("GP")]
    [InlineData("HB")]
    [InlineData("VB")]
    public void IsApplicable_TravelportUK_True(string carrier)
    {
        var actual = _target.IsApplicable(carrier, new HashSet<string>(0), "1G.0000");
        
        Assert.True(actual);
    }
}