<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.NET.Test.Sdk" Version="15.7.0" />
        <PackageReference Include="TeamCity.VSTest.TestAdapter" Version="1.0.25" />
        <PackageReference Include="xunit" Version="2.3.1" />
        <PackageReference Include="xunit.runner.visualstudio" Version="2.3.1" />
        <PackageReference Include="Moq" Version="4.7.145" />
        <PackageReference Include="FluentAssertions" Version="6.12.0" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\src\CTeleport.Services.FrequentFlyer\CTeleport.Services.FrequentFlyer.csproj" />
    </ItemGroup>

</Project>
