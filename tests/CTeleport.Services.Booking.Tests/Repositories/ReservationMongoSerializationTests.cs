using System.Collections.Generic;
using CTeleport.Services.Booking.Models;
using CTeleport.Services.Search.Shared.Models;
using MongoDB.Bson;
using MongoDB.Bson.Serialization;
using Xunit;

namespace CTeleport.Services.Booking.Tests.Repositories
{
   public class ReservationMongoSerializationTests : IClassFixture<MongoSetupFixture>
   {
       [Fact]
       public void FareRules_ShouldNotSerialize()
       {
           // Arrange
           var reservation = new Reservation
           {
               Id = "test-id",
               FareRules = new Dictionary<string, List<FareRuleSection>>
               {
                   ["LON-NYC"] = new List<FareRuleSection> { new() }
               }
           };

           // Act
           var bsonDocument = reservation.ToBsonDocument();

           // Assert
           Assert.False(bsonDocument.Contains("FareRules"));
       }

       [Fact]
       public void FareRules_ShouldDeserialize_WhenPresent()
       {
           // Arrange
           var bsonDocument = new BsonDocument
           {
               ["_id"] = "test-id",
               ["FareRules"] = new BsonDocument { ["LON-NYC"] = new BsonArray() },
               ["DepartureAt"] = System.DateTime.UtcNow
           };

           // Act
           var reservation = BsonSerializer.Deserialize<Reservation>(bsonDocument);

           // Assert
           Assert.NotNull(reservation.FareRules);
       }

       [Fact]
       public void FareRules_ShouldBeNull_WhenAbsent()
       {
           // Arrange
           var bsonDocument = new BsonDocument
           {
               ["_id"] = "test-id",
               ["DepartureAt"] = System.DateTime.UtcNow
           };

           // Act
           var reservation = BsonSerializer.Deserialize<Reservation>(bsonDocument);

           // Assert
           Assert.Null(reservation.FareRules);
       }
   }

   public class MongoSetupFixture
   {
       public MongoSetupFixture()
       {
           Infrastructure.DatabaseInitializer.RegisterClassMaps();
       }
   }
}