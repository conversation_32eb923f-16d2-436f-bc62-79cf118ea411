using AutoFixture;
using CTeleport.Services.Booking.Core.Dto;
using CTeleport.Services.Booking.Core.Interfaces;
using CTeleport.Services.Booking.Models;
using CTeleport.Services.Booking.Services;
using CTeleport.Services.Booking.Services.Interfaces;
using Moq;
using Serilog;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Xunit;

namespace CTeleport.Services.Booking.Tests.Services
{
    public class RetrieveSupplierLocatorsHandlerServiceTests
    {
        private readonly RetrieveSupplierLocatorsHandlerService _service;
        private readonly MockRepository _mockRepository;
        private readonly Fixture _fixture;

        private readonly Mock<IProviderReservationInfoService> _providerReservationServiceMock;
        private readonly Mock<IBookingMetricsService> _metricsServiceMock;
        private readonly Mock<IBookingService> _bookingServiceMock;
        private readonly Mock<ILogger> _loggerMock;

        public RetrieveSupplierLocatorsHandlerServiceTests()
        {
            _mockRepository = new MockRepository(MockBehavior.Loose);
            
            _fixture = new Fixture();
            _fixture.Customize<DateOnly>(composer => composer.FromFactory<DateTime>(DateOnly.FromDateTime));

            _bookingServiceMock = _mockRepository.Create<IBookingService>();
            _metricsServiceMock = _mockRepository.Create<IBookingMetricsService>();
            _providerReservationServiceMock = _mockRepository.Create<IProviderReservationInfoService>();
            _loggerMock = _mockRepository.Create<ILogger>();

            _service = new RetrieveSupplierLocatorsHandlerService(
                _providerReservationServiceMock.Object,
                _metricsServiceMock.Object,
                _bookingServiceMock.Object,
                _loggerMock.Object);
        }

        [Fact]
        public async Task SetSupplierLocatorsForAllReservationsWithoutItAsync_ExpectNoSupplierLocatorsSet_WhenNoReservationsWithoutSupplierLocatorsExist()
        {
            // Arrange
            _bookingServiceMock
                .Setup(i => i.GetAllReservationsWithoutSupplierLocatorsAsync())
                .ReturnsAsync(Array.Empty<Reservation>());

            // Act
            await _service.SetSupplierLocatorsForAllReservationsWithoutItAsync();

            // Assert
            _bookingServiceMock.Verify(i => i.GetAllReservationsWithoutSupplierLocatorsAsync(), Times.Once);
            _providerReservationServiceMock.Verify(i => i.GetReservationAsync(It.IsAny<ProviderRetrieveReservationRequest>()), Times.Never);
            _bookingServiceMock.Verify(i => i.SetSupplierLocatorsAsync(It.IsAny<string>(), It.IsAny<Dictionary<string, string>>()), Times.Never);
        }

        [Fact]
        public async Task SetSupplierLocatorsForAllReservationsWithoutItAsync_ExpectNoSupplierLocatorsSet_WhenFlightReservationNotFound()
        {
            // Arrange
            var reservations = new[] {
                _fixture.Create<Reservation>()
            };

            ProviderRetrieveReservationResponse reservationResponse = null;

            _bookingServiceMock
                .Setup(i => i.GetAllReservationsWithoutSupplierLocatorsAsync())
                .ReturnsAsync(reservations);

            _providerReservationServiceMock
                .Setup(i => i.GetReservationAsync(It.IsAny<ProviderRetrieveReservationRequest>()))
                .ReturnsAsync(reservationResponse);

            // Act
            await _service.SetSupplierLocatorsForAllReservationsWithoutItAsync();

            // Assert
            _bookingServiceMock.Verify(i => i.GetAllReservationsWithoutSupplierLocatorsAsync(), Times.Once);
            _providerReservationServiceMock.Verify(i => i.GetReservationAsync(It.IsAny<ProviderRetrieveReservationRequest>()), Times.Once);
            _bookingServiceMock.Verify(i => i.SetSupplierLocatorsAsync(It.IsAny<string>(), It.IsAny<Dictionary<string, string>>()), Times.Never);
        }

        [Fact]
        public async Task SetSupplierLocatorsForAllReservationsWithoutItAsync_ExpectSetSupplierLocators_WhenFlightReservationHasSupplierLocators()
        {
            // Arrange
            var reservations = new[] {
                _fixture.Create<Reservation>()
            };

            var reservationResponse = _fixture.Create<ProviderRetrieveReservationResponse>();

            _bookingServiceMock
                .Setup(i => i.GetAllReservationsWithoutSupplierLocatorsAsync())
                .ReturnsAsync(reservations);

            _providerReservationServiceMock
                .Setup(i => i.GetReservationAsync(It.IsAny<ProviderRetrieveReservationRequest>()))
                .ReturnsAsync(reservationResponse);

            // Act
            await _service.SetSupplierLocatorsForAllReservationsWithoutItAsync();

            // Assert
            _bookingServiceMock.Verify(i => i.GetAllReservationsWithoutSupplierLocatorsAsync(), Times.Once);
            _providerReservationServiceMock.Verify(i => i.GetReservationAsync(It.IsAny<ProviderRetrieveReservationRequest>()), Times.Once);
            _bookingServiceMock.Verify(i => i.SetSupplierLocatorsAsync(It.IsAny<string>(), It.IsAny<Dictionary<string, string>>()), Times.Once);
        }

        [Fact]
        public async Task SetSupplierLocatorsForAllReservationsWithoutItAsync_ExpectNoSupplierLocatorsSet_WhenFlightReservationDoesNotHaveSupplierLocators()
        {
            // Arrange
            var reservations = new[] {
                _fixture.Create<Reservation>()
            };

            var reservationResponse = _fixture.Create<ProviderRetrieveReservationResponse>();
            reservationResponse.FlightReservation.SupplierLocators = new();

            _bookingServiceMock
                .Setup(i => i.GetAllReservationsWithoutSupplierLocatorsAsync())
                .ReturnsAsync(reservations);

            _providerReservationServiceMock
                .Setup(i => i.GetReservationAsync(It.IsAny<ProviderRetrieveReservationRequest>()))
                .ReturnsAsync(reservationResponse);

            // Act
            await _service.SetSupplierLocatorsForAllReservationsWithoutItAsync();

            // Assert
            _bookingServiceMock.Verify(i => i.GetAllReservationsWithoutSupplierLocatorsAsync(), Times.Once);
            _providerReservationServiceMock.Verify(i => i.GetReservationAsync(It.IsAny<ProviderRetrieveReservationRequest>()), Times.Once);
            _bookingServiceMock.Verify(i => i.SetSupplierLocatorsAsync(It.IsAny<string>(), It.IsAny<Dictionary<string, string>>()), Times.Never);
        }
    }
}
