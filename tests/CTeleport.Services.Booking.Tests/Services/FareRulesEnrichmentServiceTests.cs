using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using CTeleport.Services.Booking.Models;
using CTeleport.Services.Booking.Services;
using CTeleport.Services.FareRules;
using CTeleport.Services.FareRules.Models;
using CTeleport.Services.Search.Shared.Models;
using FluentAssertions;
using Moq;
using Serilog;
using Xunit;

namespace CTeleport.Services.Booking.Tests.Services
{
    public class FareRulesEnrichmentServiceTests
    {
        private readonly Mock<IFareRulesClient> _fareRulesClientMock;
        private readonly Mock<ILogger> _loggerMock;
        private readonly FareRulesEnrichmentService _service;

        public FareRulesEnrichmentServiceTests()
        {
            _fareRulesClientMock = new Mock<IFareRulesClient>();
            _loggerMock = new Mock<ILogger>();
            _service = new FareRulesEnrichmentService(_fareRulesClientMock.Object, _loggerMock.Object);
        }

        [Fact]
        public void Constructor_WithNullFareRulesClient_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new FareRulesEnrichmentService(null, _loggerMock.Object));
        }

        [Fact]
        public void Constructor_WithNullLogger_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new FareRulesEnrichmentService(_fareRulesClientMock.Object, null));
        }

        [Fact]
        public async Task EnrichFareRulesAsync_WithNullReservation_LogsWarningAndReturns()
        {
            // Act
            await _service.EnrichFareRulesAsync(null);

            // Assert
            _loggerMock.Verify(
                x => x.Warning("Reservation is null, skipping FareRules enrichment"),
                Times.Once);
            _fareRulesClientMock.Verify(x => x.QueryFareRuleSectionsByIdsAsync(It.IsAny<List<string>>(), It.IsAny<CancellationToken>()), Times.Never);
        }

        [Fact]
        public async Task EnrichFareRulesAsync_WithNullFareRulesIds_LogsInfoAndReturns()
        {
            // Arrange
            var reservation = new Reservation { Id = "test-id", FareRulesIds = null };

            // Act
            await _service.EnrichFareRulesAsync(reservation);

            // Assert
            _loggerMock.Verify(
                x => x.Information("No FareRulesIds found for reservation {ReservationId}, skipping enrichment", "test-id"),
                Times.Once);
            _fareRulesClientMock.Verify(x => x.QueryFareRuleSectionsByIdsAsync(It.IsAny<List<string>>(), It.IsAny<CancellationToken>()), Times.Never);
        }

        [Fact]
        public async Task EnrichFareRulesAsync_WithEmptyFareRulesIds_LogsInfoAndReturns()
        {
            // Arrange
            var reservation = new Reservation { Id = "test-id", FareRulesIds = new Dictionary<string, List<string>>() };

            // Act
            await _service.EnrichFareRulesAsync(reservation);

            // Assert
            _loggerMock.Verify(
                x => x.Information("No FareRulesIds found for reservation {ReservationId}, skipping enrichment", "test-id"),
                Times.Once);
            _fareRulesClientMock.Verify(x => x.QueryFareRuleSectionsByIdsAsync(It.IsAny<List<string>>(), It.IsAny<CancellationToken>()), Times.Never);
        }

        [Fact]
        public async Task EnrichFareRulesAsync_WithValidFareRulesIds_CallsApiAndEnrichesReservation()
        {
            // Arrange
            var reservation = new Reservation
            {
                Id = "test-id",
                FareRulesIds = new Dictionary<string, List<string>>
                {
                    ["SVO-VVO"] = new List<string> { "rule1", "rule2" },
                    ["VVO-SVO"] = new List<string> { "rule3" }
                }
            };

            var apiResponse = new List<FareRuleSectionDto>
            {
                new FareRuleSectionDto { Category = "16", Title = "Penalties", Text = "Rule 1 text" },
                new FareRuleSectionDto { Category = "8", Title = "Stopovers", Text = "Rule 2 text" },
                new FareRuleSectionDto { Category = "16", Title = "Penalties", Text = "Rule 3 text" }
            };

            _fareRulesClientMock
                .Setup(x => x.QueryFareRuleSectionsByIdsAsync(It.IsAny<List<string>>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(apiResponse);

            // Act
            await _service.EnrichFareRulesAsync(reservation);

            // Assert
            _fareRulesClientMock.Verify(
                x => x.QueryFareRuleSectionsByIdsAsync(
                    It.Is<List<string>>(ids => ids.Count == 3 && ids.Contains("rule1") && ids.Contains("rule2") && ids.Contains("rule3")),
                    It.IsAny<CancellationToken>()),
                Times.Once);

            reservation.FareRules.Should().NotBeNull();
            reservation.FareRules.Should().HaveCount(2);
            reservation.FareRules.Should().ContainKey("SVO-VVO");
            reservation.FareRules.Should().ContainKey("VVO-SVO");

            _loggerMock.Verify(
                x => x.Information("Starting FareRules enrichment for reservation {ReservationId}", "test-id"),
                Times.Once);
            _loggerMock.Verify(
                x => x.Information("Requesting {Count} fare rules for reservation {ReservationId}", 3, "test-id"),
                Times.Once);
            _loggerMock.Verify(
                x => x.Information("Received {Count} fare rule sections from API for reservation {ReservationId}", 3, "test-id"),
                Times.Once);
        }

        [Fact]
        public async Task EnrichFareRulesAsync_WithDuplicateFareRuleIds_DeduplicatesBeforeApiCall()
        {
            // Arrange
            var reservation = new Reservation
            {
                Id = "test-id",
                FareRulesIds = new Dictionary<string, List<string>>
                {
                    ["SVO-VVO"] = new List<string> { "rule1", "rule2" },
                    ["VVO-SVO"] = new List<string> { "rule1", "rule3" } // rule1 is duplicated
                }
            };

            var apiResponse = new List<FareRuleSectionDto>
            {
                new FareRuleSectionDto { Category = "16", Title = "Penalties", Text = "Rule 1 text" },
                new FareRuleSectionDto { Category = "8", Title = "Stopovers", Text = "Rule 2 text" },
                new FareRuleSectionDto { Category = "16", Title = "Penalties", Text = "Rule 3 text" }
            };

            _fareRulesClientMock
                .Setup(x => x.QueryFareRuleSectionsByIdsAsync(It.IsAny<List<string>>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(apiResponse);

            // Act
            await _service.EnrichFareRulesAsync(reservation);

            // Assert
            _fareRulesClientMock.Verify(
                x => x.QueryFareRuleSectionsByIdsAsync(
                    It.Is<List<string>>(ids => ids.Count == 3 && ids.Distinct().Count() == 3), // Should be deduplicated
                    It.IsAny<CancellationToken>()),
                Times.Once);
        }

        [Fact]
        public async Task EnrichFareRulesAsync_WithEmptyApiResponse_LogsWarningAndReturns()
        {
            // Arrange
            var reservation = new Reservation
            {
                Id = "test-id",
                FareRulesIds = new Dictionary<string, List<string>>
                {
                    ["SVO-VVO"] = new List<string> { "rule1" }
                }
            };

            _fareRulesClientMock
                .Setup(x => x.QueryFareRuleSectionsByIdsAsync(It.IsAny<List<string>>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new List<FareRuleSectionDto>());

            // Act
            await _service.EnrichFareRulesAsync(reservation);

            // Assert
            _loggerMock.Verify(
                x => x.Warning("No fare rule sections returned from API for reservation {ReservationId}", "test-id"),
                Times.Once);
        }

        [Fact]
        public async Task EnrichFareRulesAsync_WithApiException_LogsErrorAndRethrows()
        {
            // Arrange
            var reservation = new Reservation
            {
                Id = "test-id",
                FareRulesIds = new Dictionary<string, List<string>>
                {
                    ["SVO-VVO"] = new List<string> { "rule1" }
                }
            };

            var exception = new Exception("API error");
            _fareRulesClientMock
                .Setup(x => x.QueryFareRuleSectionsByIdsAsync(It.IsAny<List<string>>(), It.IsAny<CancellationToken>()))
                .ThrowsAsync(exception);

            // Act & Assert
            var thrownException = await Assert.ThrowsAsync<Exception>(() => _service.EnrichFareRulesAsync(reservation));
            thrownException.Should().Be(exception);

            _loggerMock.Verify(
                x => x.Error(exception, "Error enriching FareRules for reservation {ReservationId}", "test-id"),
                Times.Once);
        }

        [Fact]
        public async Task EnrichFareRulesAsync_WithExistingFareRules_InitializesIfNull()
        {
            // Arrange
            var reservation = new Reservation
            {
                Id = "test-id",
                FareRulesIds = new Dictionary<string, List<string>>
                {
                    ["SVO-VVO"] = new List<string> { "rule1" }
                },
                FareRules = null // Should be initialized
            };

            var apiResponse = new List<FareRuleSectionDto>
            {
                new FareRuleSectionDto { Category = "16", Title = "Penalties", Text = "Rule 1 text" }
            };

            _fareRulesClientMock
                .Setup(x => x.QueryFareRuleSectionsByIdsAsync(It.IsAny<List<string>>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(apiResponse);

            // Act
            await _service.EnrichFareRulesAsync(reservation);

            // Assert
            reservation.FareRules.Should().NotBeNull();
            reservation.FareRules.Should().HaveCount(1);
        }
    }
}
