using System;
using System.Linq;
using System.Threading.Tasks;
using AutoFixture;
using CTeleport.Messages.Commands.Reservations;
using CTeleport.Messages.Events.Bookings;
using CTeleport.Services.Booking.Api;
using CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Enums;
using CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Requests;
using CTeleport.Services.Booking.Models;
using CTeleport.Services.Booking.Services;
using CTeleport.Services.Booking.Services.Interfaces;
using CTeleport.Services.Search.Shared.Models;
using FluentAssertions;
using Moq;
using Serilog.Core;
using Xunit;
using BookingSaga = CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Booking;
using ReservationState = CTeleport.Services.Booking.Enums.ReservationState;

namespace CTeleport.Services.Booking.Tests.Services;

public class ApprovalFlowServiceTests
{
    private readonly MockRepository _mockRepository;
    private readonly Fixture _fixture;
    
    private readonly Mock<IBookingApprovalItemsService> _bookingApprovalItemsServiceMock;
    private readonly Mock<IBrokenReservationService> _brokenReservationServiceMock;
    private readonly Mock<IBookingSagaService> _bookingSagaServiceMock;
    private readonly Mock<IBookingMetricsService> _metricsServiceMock;
    private readonly Mock<IBookingService> _bookingServiceMock;
    
    private readonly IApprovalFlowService _service;

    public ApprovalFlowServiceTests()
    {
        _mockRepository = new MockRepository(MockBehavior.Loose);
        
        _fixture = new Fixture();
        _fixture.Customize<DateOnly>(composer => composer.FromFactory<DateTime>(DateOnly.FromDateTime));

        _bookingApprovalItemsServiceMock = _mockRepository.Create<IBookingApprovalItemsService>();
        _brokenReservationServiceMock = _mockRepository.Create<IBrokenReservationService>();
        _bookingSagaServiceMock = _mockRepository.Create<IBookingSagaService>();
        _metricsServiceMock = _mockRepository.Create<IBookingMetricsService>();
        _bookingServiceMock = _mockRepository.Create<IBookingService>();
        var mapper = AutoMapperConfig.InitializeMapper();

        _service = new ApprovalFlowService(
            _bookingApprovalItemsServiceMock.Object,
            _brokenReservationServiceMock.Object,
            _bookingSagaServiceMock.Object,
            _metricsServiceMock.Object,
            _bookingServiceMock.Object,
            mapper,
            Logger.None);
    }
    
    [Fact]
    public async Task ApproveBookingAsync_ExpectProcessDefaultApprovalFlow_WhenBookingHasOneNonVirtualReservation()
    {
        // Arrange
        var nonVirtualReservation = _fixture.Create<Reservation>();
        nonVirtualReservation.IsVirtual = false;
        nonVirtualReservation.ApprovalRequired = true;
        
        var requestId = _fixture.Create<string>();
        var tenantId = _fixture.Create<string>();
        var originalSolution = _fixture.Create<FlightSolution>();
        var booking = _fixture.Create<Models.Booking>();
        var reservations = new [] { nonVirtualReservation };
        var bookingSaga = _fixture.Create<BookingSaga>();
        var approvedBy = _fixture.Create<Messages.Commands.Models.User>();
        
        var request = new BookingApprovedRequest
        {
            RequestId = requestId,
            TenantId = tenantId,
            Booking = booking,
            BookingSaga = bookingSaga,
            ApprovedBy = approvedBy,
            Reservations = reservations,
            OriginalSolution = originalSolution
        };

        _bookingApprovalItemsServiceMock
            .Setup(i => 
                i.GetApprovalQueueItemRepricingRequiredAsync(It.IsAny<string>(), It.IsAny<bool>()))
            .ReturnsAsync(false);
        
        _bookingSagaServiceMock
            .Setup(i => i.ApproveAsync(It.IsAny<ApproveSagaRequest>()))
            .ReturnsAsync(bookingSaga);

        _bookingSagaServiceMock
            .Setup(i => i.ChangeStateAsync(It.IsAny<string>(), BookingSagaState.Completed))
            .ReturnsAsync(bookingSaga);
        
        // Act
        var result = await _service.ApproveBookingAsync(request);

        // Assert
        result.Should().NotBeNullOrEmpty();
        result.Should().HaveCount(2);
        result[0].Should().BeOfType<ResetTicketingTime>();
        result[1].Should().BeOfType<BookingConfirmed>();
        
        _bookingApprovalItemsServiceMock.Verify(i => 
            i.GetApprovalQueueItemRepricingRequiredAsync(It.IsAny<string>(), It.Is<bool>(j => j == true)), Times.Once);

        _bookingServiceMock.Verify(i => 
            i.UpdateReservationAsync(It.IsAny<Reservation>()), Times.Once);

        _bookingServiceMock.Verify(i => 
            i.SetAsApprovedByAsync(It.IsAny<string>(), It.IsAny<User[]>()), Times.Once);

        _bookingSagaServiceMock.Verify(i => 
            i.ApproveAsync(It.IsAny<ApproveSagaRequest>()), Times.Once);

        _bookingSagaServiceMock.Verify(i => 
            i.ChangeStateAsync(It.IsAny<string>(), It.Is<BookingSagaState>(j => j == BookingSagaState.Completed)), Times.Once);

        _metricsServiceMock.Verify(i => 
            i.DecrementActiveBookingSagaCounter(It.IsAny<string>()), Times.Once);
        
        _brokenReservationServiceMock.Verify(i => 
            i.ReservationBroken(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<Messages.Commands.Models.User>()), Times.Never);
    }
    
    [Fact]
    public async Task ApproveBookingAsync_ExpectDispatchApprovalActionRequired_WhenBookingHasOneNonVirtualReservationAndRepricingRequired()
    {
        // Arrange
        var nonVirtualReservation = _fixture.Create<Reservation>();
        nonVirtualReservation.IsVirtual = false;
        nonVirtualReservation.ApprovalRequired = true;
        
        var request = new BookingApprovedRequest
        {
            RequestId = _fixture.Create<string>(),
            TenantId = _fixture.Create<string>(),
            Booking = _fixture.Create<Models.Booking>(),
            BookingSaga =  _fixture.Create<BookingSaga>(),
            ApprovedBy = _fixture.Create<Messages.Commands.Models.User>(),
            Reservations = new [] { nonVirtualReservation }
        };

        _bookingApprovalItemsServiceMock
            .Setup(i => 
                i.GetApprovalQueueItemRepricingRequiredAsync(It.IsAny<string>(), It.IsAny<bool>()))
            .ReturnsAsync(true);
        
        // Act
        var result = await _service.ApproveBookingAsync(request);

        // Assert
        result.Should().NotBeNullOrEmpty();
        result.Should().HaveCount(1);
        result.First().Should().BeOfType<ApproverActionRequired>();
        
        _bookingApprovalItemsServiceMock.Verify(i => 
            i.GetApprovalQueueItemRepricingRequiredAsync(It.IsAny<string>(), It.IsAny<bool>()), Times.Once);

        _bookingServiceMock.Verify(i => 
            i.UpdateReservationAsync(It.IsAny<Reservation>()), Times.Never);

        _bookingServiceMock.Verify(i => 
            i.SetAsApprovedByAsync(It.IsAny<string>(), It.IsAny<User[]>()), Times.Never);

        _bookingSagaServiceMock.Verify(i => 
            i.ApproveAsync(It.IsAny<ApproveSagaRequest>()), Times.Never);

        _bookingSagaServiceMock.Verify(i => 
            i.ChangeStateAsync(It.IsAny<string>(), It.IsAny<BookingSagaState>()), Times.Never);

        _metricsServiceMock.Verify(i => 
            i.DecrementActiveBookingSagaCounter(It.IsAny<string>()), Times.Never);
        
        _brokenReservationServiceMock.Verify(i => 
            i.ReservationBroken(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<Messages.Commands.Models.User>()), Times.Never);
    }
    
    [Fact]
    public async Task ApproveBookingAsync_ExpectProcessVirtualApprovalFlow_WhenBookingHasOneVirtualReservation()
    {
        // Arrange
        var virtualReservation = _fixture.Create<Reservation>();
        virtualReservation.IsVirtual = true;
        virtualReservation.ApprovalRequired = true;
        
        var tenantId = _fixture.Create<string>();
        var booking = _fixture.Create<Models.Booking>();
        
        var approvedBy = _fixture.Create<Messages.Commands.Models.User>();
        var bookingSaga = new BookingSaga(booking.Id, tenantId)
            .Initialize(_fixture.Build<InitializeBookingRequest>()
                .With(x => x.IsApprovalRequired, true)
                .With(x => x.PaymentMethod, PaymentMethod.CreditCard)
                .Create())
            .AddReservation(_fixture.Build<AddReservationRequest>()
                .With(x => x.ReservationId, virtualReservation.Id)
                .Create());
        
        var request = new BookingApprovedRequest
        {
            RequestId = _fixture.Create<string>(),
            TenantId = tenantId,
            Booking = booking,
            BookingSaga = bookingSaga,
            ApprovedBy = approvedBy,
            Reservations = new [] { virtualReservation },
            OriginalSolution = _fixture.Create<FlightSolution>()
        };

        _bookingApprovalItemsServiceMock
            .Setup(i => 
                i.GetApprovalQueueItemRepricingRequiredAsync(It.IsAny<string>(), It.IsAny<bool>()))
            .ReturnsAsync(false);
        
        _bookingSagaServiceMock
            .Setup(i => i.ApproveAsync(It.IsAny<ApproveSagaRequest>()))
            .ReturnsAsync(bookingSaga);

        _bookingSagaServiceMock
            .Setup(i => i.ChangeStateAsync(It.IsAny<string>(), BookingSagaState.Completed))
            .ReturnsAsync(bookingSaga);
        
        // Act
        var result = await _service.ApproveBookingAsync(request);

        // Assert
        result.Should().NotBeNullOrEmpty();
        result.Should().HaveCount(1);
        result.First().Should().BeOfType<CreateReservation>();
        
        _bookingApprovalItemsServiceMock.Verify(i => 
            i.GetApprovalQueueItemRepricingRequiredAsync(It.IsAny<string>(), It.Is<bool>(j => j == true)), Times.Never);

        _bookingServiceMock.Verify(i => 
            i.UpdateReservationAsync(It.IsAny<Reservation>()), Times.Never);

        _bookingServiceMock.Verify(i => 
            i.SetAsApprovedByAsync(It.IsAny<string>(), It.IsAny<User[]>()), Times.Never);

        _bookingSagaServiceMock.Verify(i => 
            i.ApproveAsync(It.IsAny<ApproveSagaRequest>()), Times.Once);

        _bookingSagaServiceMock.Verify(i => 
            i.ChangeStateAsync(It.IsAny<string>(), It.Is<BookingSagaState>(j => j == BookingSagaState.Completed)), Times.Never);

        _metricsServiceMock.Verify(i => 
            i.DecrementActiveBookingSagaCounter(It.IsAny<string>()), Times.Never);
        
        _brokenReservationServiceMock.Verify(i => 
            i.ReservationBroken(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<Messages.Commands.Models.User>()), Times.Never);
    }
    
    [Fact]
    public async Task ApproveBookingAsync_ExpectProcessDefaultApprovalFlow_WhenBookingHasTwoNonVirtualReservation()
    {
        // Arrange
        var nonVirtualReservation = _fixture.Create<Reservation>();
        nonVirtualReservation.IsVirtual = false;
        nonVirtualReservation.ApprovalRequired = true;
        
        var nonVirtualReservation2 = _fixture.Create<Reservation>();
        nonVirtualReservation2.IsVirtual = false;
        nonVirtualReservation2.ApprovalRequired = true;
        
        var requestId = _fixture.Create<string>();
        var tenantId = _fixture.Create<string>();
        var originalSolution = _fixture.Create<FlightSolution>();
        var booking = _fixture.Create<Models.Booking>();
        var reservations = new [] { nonVirtualReservation, nonVirtualReservation2 };
        var bookingSaga = _fixture.Create<BookingSaga>();
        var approvedBy = _fixture.Create<Messages.Commands.Models.User>();
        
        var request = new BookingApprovedRequest
        {
            RequestId = requestId,
            TenantId = tenantId,
            Booking = booking,
            BookingSaga = bookingSaga,
            ApprovedBy = approvedBy,
            Reservations = reservations,
            OriginalSolution = originalSolution
        };

        _bookingApprovalItemsServiceMock
            .Setup(i => 
                i.GetApprovalQueueItemRepricingRequiredAsync(It.IsAny<string>(), It.IsAny<bool>()))
            .ReturnsAsync(false);
        
        _bookingSagaServiceMock
            .Setup(i => i.ApproveAsync(It.IsAny<ApproveSagaRequest>()))
            .ReturnsAsync(bookingSaga);

        _bookingSagaServiceMock
            .Setup(i => i.ChangeStateAsync(It.IsAny<string>(), BookingSagaState.Completed))
            .ReturnsAsync(bookingSaga);
        
        // Act
        var result = await _service.ApproveBookingAsync(request);

        // Assert
        result.Should().NotBeNullOrEmpty();
        result.Should().HaveCount(3);
        result[0].Should().BeOfType<ResetTicketingTime>();
        result[1].Should().BeOfType<ResetTicketingTime>();
        result[2].Should().BeOfType<BookingConfirmed>();
        
        _bookingApprovalItemsServiceMock.Verify(i => 
            i.GetApprovalQueueItemRepricingRequiredAsync(It.IsAny<string>(), It.Is<bool>(j => j == true)), Times.Once);

        _bookingServiceMock.Verify(i => 
            i.UpdateReservationAsync(It.IsAny<Reservation>()), Times.Exactly(2));

        _bookingServiceMock.Verify(i => 
            i.SetAsApprovedByAsync(It.IsAny<string>(), It.IsAny<User[]>()), Times.Once);

        _bookingSagaServiceMock.Verify(i => 
            i.ApproveAsync(It.IsAny<ApproveSagaRequest>()), Times.Once);

        _bookingSagaServiceMock.Verify(i => 
            i.ChangeStateAsync(It.IsAny<string>(), It.Is<BookingSagaState>(j => j == BookingSagaState.Completed)), Times.Once);

        _metricsServiceMock.Verify(i => 
            i.DecrementActiveBookingSagaCounter(It.IsAny<string>()), Times.Once);
        
        _brokenReservationServiceMock.Verify(i => 
            i.ReservationBroken(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<Messages.Commands.Models.User>()), Times.Never);
    }
    
    
    [Fact]
    public async Task ApproveBookingAsync_ExpectProcessVirtualApprovalFlow_WhenBookingHasTwoVirtualReservation()
    {
        // Arrange
        var virtualReservation = _fixture.Create<Reservation>();
        virtualReservation.IsVirtual = true;
        virtualReservation.ApprovalRequired = true;
        
        var virtualReservation2 = _fixture.Create<Reservation>();
        virtualReservation2.IsVirtual = true;
        virtualReservation2.ApprovalRequired = true;
        
        var tenantId = _fixture.Create<string>();
        var booking = _fixture.Create<Models.Booking>();
        
        var approvedBy = _fixture.Create<Messages.Commands.Models.User>();
        var bookingSaga = new BookingSaga(booking.Id, tenantId)
            .Initialize(_fixture.Build<InitializeBookingRequest>()
                .With(x => x.IsApprovalRequired, true)
                .With(x => x.PaymentMethod, PaymentMethod.CreditCard)
                .Create())
            .AddReservation(_fixture.Build<AddReservationRequest>()
                .With(x => x.ReservationId, virtualReservation.Id)
                .Create())
            .AddReservation(_fixture.Build<AddReservationRequest>()
                .With(x => x.ReservationId, virtualReservation2.Id)
                .Create());
        
        var request = new BookingApprovedRequest
        {
            RequestId = _fixture.Create<string>(),
            TenantId = tenantId,
            Booking = booking,
            BookingSaga = bookingSaga,
            ApprovedBy = approvedBy,
            Reservations = new [] { virtualReservation, virtualReservation2 },
            OriginalSolution = _fixture.Create<FlightSolution>()
        };

        _bookingApprovalItemsServiceMock
            .Setup(i => 
                i.GetApprovalQueueItemRepricingRequiredAsync(It.IsAny<string>(), It.IsAny<bool>()))
            .ReturnsAsync(false);
        
        _bookingSagaServiceMock
            .Setup(i => i.ApproveAsync(It.IsAny<ApproveSagaRequest>()))
            .ReturnsAsync(bookingSaga);

        _bookingSagaServiceMock
            .Setup(i => i.ChangeStateAsync(It.IsAny<string>(), BookingSagaState.Completed))
            .ReturnsAsync(bookingSaga);
        
        // Act
        var result = await _service.ApproveBookingAsync(request);

        // Assert
        result.Should().NotBeNullOrEmpty();
        result.Should().HaveCount(1);
        result.First().Should().BeOfType<CreateReservation>();
        
        _bookingApprovalItemsServiceMock.Verify(i => 
            i.GetApprovalQueueItemRepricingRequiredAsync(It.IsAny<string>(), It.Is<bool>(j => j == true)), Times.Never);

        _bookingServiceMock.Verify(i => 
            i.UpdateReservationAsync(It.IsAny<Reservation>()), Times.Never);

        _bookingServiceMock.Verify(i => 
            i.SetAsApprovedByAsync(It.IsAny<string>(), It.IsAny<User[]>()), Times.Never);

        _bookingSagaServiceMock.Verify(i => 
            i.ApproveAsync(It.IsAny<ApproveSagaRequest>()), Times.Once);

        _bookingSagaServiceMock.Verify(i => 
            i.ChangeStateAsync(It.IsAny<string>(), It.Is<BookingSagaState>(j => j == BookingSagaState.Completed)), Times.Never);

        _metricsServiceMock.Verify(i => 
            i.DecrementActiveBookingSagaCounter(It.IsAny<string>()), Times.Never);
        
        _brokenReservationServiceMock.Verify(i => 
            i.ReservationBroken(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<Messages.Commands.Models.User>()), Times.Never);
    }
    
    [Fact]
    public async Task ApproveBookingAsync_ExpectProcessMainApprovalFlow_WhenBookingHasOneNonVirtualOneVirtualReservation()
    {
        // Arrange
        var virtualReservation = _fixture.Create<Reservation>();
        virtualReservation.IsVirtual = true;
        virtualReservation.ApprovalRequired = true;
        
        var nonVirtualReservation = _fixture.Create<Reservation>();
        nonVirtualReservation.IsVirtual = false;
        nonVirtualReservation.ApprovalRequired = true;
        
        var tenantId = _fixture.Create<string>();
        var booking = _fixture.Create<Models.Booking>();
        
        var approvedBy = _fixture.Create<Messages.Commands.Models.User>();
        var bookingSaga = new BookingSaga(booking.Id, tenantId)
            .Initialize(_fixture.Build<InitializeBookingRequest>()
                .With(x => x.IsApprovalRequired, true)
                .With(x => x.PaymentMethod, PaymentMethod.CreditCard)
                .Create())
            .AddReservation(_fixture.Build<AddReservationRequest>()
                .With(x => x.ReservationId, virtualReservation.Id)
                .Create())
            .AddReservation(_fixture.Build<AddReservationRequest>()
                .With(x => x.ReservationId, nonVirtualReservation.Id)
                .Create());
        
        var request = new BookingApprovedRequest
        {
            RequestId = _fixture.Create<string>(),
            TenantId = tenantId,
            Booking = booking,
            BookingSaga = bookingSaga,
            ApprovedBy = approvedBy,
            Reservations = new [] { virtualReservation, nonVirtualReservation },
            OriginalSolution = _fixture.Create<FlightSolution>()
        };

        _bookingApprovalItemsServiceMock
            .Setup(i => 
                i.GetApprovalQueueItemRepricingRequiredAsync(It.IsAny<string>(), It.IsAny<bool>()))
            .ReturnsAsync(false);
        
        _bookingSagaServiceMock
            .Setup(i => i.ApproveAsync(It.IsAny<ApproveSagaRequest>()))
            .ReturnsAsync(bookingSaga);

        _bookingSagaServiceMock
            .Setup(i => i.ChangeStateAsync(It.IsAny<string>(), BookingSagaState.Completed))
            .ReturnsAsync(bookingSaga);
        
        // Act
        var result = await _service.ApproveBookingAsync(request);

        // Assert
        result.Should().NotBeNullOrEmpty();
        result.Should().HaveCount(2);
        result[0].Should().BeOfType<ResetTicketingTime>();
        result[1].Should().BeOfType<CreateReservation>();
        
        _bookingApprovalItemsServiceMock.Verify(i => 
            i.GetApprovalQueueItemRepricingRequiredAsync(It.IsAny<string>(), It.Is<bool>(j => j == true)), Times.Once);

        _bookingServiceMock.Verify(i => 
            i.UpdateReservationAsync(It.IsAny<Reservation>()), Times.Exactly(1));

        _bookingServiceMock.Verify(i => 
            i.SetAsApprovedByAsync(It.IsAny<string>(), It.IsAny<User[]>()), Times.Once);

        _bookingSagaServiceMock.Verify(i => 
            i.ApproveAsync(It.IsAny<ApproveSagaRequest>()), Times.Once);

        _bookingSagaServiceMock.Verify(i => 
            i.ChangeStateAsync(It.IsAny<string>(), It.Is<BookingSagaState>(j => j == BookingSagaState.Completed)), Times.Never);

        _metricsServiceMock.Verify(i => 
            i.DecrementActiveBookingSagaCounter(It.IsAny<string>()), Times.Never);
        
        _brokenReservationServiceMock.Verify(i => 
            i.ReservationBroken(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<Messages.Commands.Models.User>()), Times.Never);
    }
    
    [Fact]
    public async Task ApproveBookingAsync_ExpectProcessInconsistentBookingFlow_WhenBookingIsRepricedTwice()
    {
        // Arrange
        var nonVirtualReservation = _fixture.Create<Reservation>();
        nonVirtualReservation.IsVirtual = false;
        nonVirtualReservation.ApprovalRequired = true;
        nonVirtualReservation.State = ReservationState.Active;
        
        var requestId = _fixture.Create<string>();
        var tenantId = _fixture.Create<string>();
        var originalSolution = _fixture.Create<FlightSolution>();
        originalSolution.SplitProviderODs = originalSolution.SplitProviderODs.Take(1).ToList();
        var approvedSolution = originalSolution;
        var booking = _fixture.Create<Models.Booking>();
        var reservations = new [] { nonVirtualReservation };
        
        var bookingSaga = new BookingSaga(booking.Id, tenantId)
            .Initialize(_fixture.Build<InitializeBookingRequest>()
                .With(x => x.IsApprovalRequired, true)
                .With(x => x.PaymentMethod, PaymentMethod.CreditCard)
                .Create())
            .AddReservation(_fixture.Build<AddReservationRequest>()
                .With(x => x.ReservationId, nonVirtualReservation.Id)
                .With(x => x.ProviderKey, originalSolution.SplitProviderODs.First().ProviderKey)
                .Create());
        
        var approvedBy = _fixture.Create<Messages.Commands.Models.User>();
        
        var request = new BookingApprovedRequest
        {
            RequestId = requestId,
            TenantId = tenantId,
            Booking = booking,
            BookingSaga = bookingSaga,
            ApprovedBy = approvedBy,
            Reservations = reservations,
            ApprovedSolution = approvedSolution,
            OriginalSolution = originalSolution
        };

        _bookingApprovalItemsServiceMock
            .Setup(i => 
                i.GetApprovalQueueItemRepricingRequiredAsync(It.IsAny<string>(), It.IsAny<bool>()))
            .ReturnsAsync(true);
        
        _bookingSagaServiceMock
            .Setup(i => i.ApproveAsync(It.IsAny<ApproveSagaRequest>()))
            .ReturnsAsync(bookingSaga);

        _bookingSagaServiceMock
            .Setup(i => i.ChangeStateAsync(It.IsAny<string>(), BookingSagaState.Completed))
            .ReturnsAsync(bookingSaga);
        
        _bookingSagaServiceMock
            .Setup(i => i.GetAsync(It.IsAny<string>()))
            .ReturnsAsync(bookingSaga);
        
        // Act
        var result = await _service.ApproveBookingAsync(request);

        // Assert
        result.Should().NotBeNullOrEmpty();
        result.Should().HaveCount(1);
        result[0].Should().BeOfType<CreateReservation>();
        
        _bookingApprovalItemsServiceMock.Verify(i => 
            i.GetApprovalQueueItemRepricingRequiredAsync(It.IsAny<string>(), It.Is<bool>(j => j == true)), Times.Once);

        _bookingServiceMock.Verify(i => 
            i.UpdateReservationAsync(It.IsAny<Reservation>()), Times.Never);

        _bookingServiceMock.Verify(i => 
            i.SetAsApprovedByAsync(It.IsAny<string>(), It.IsAny<User[]>()), Times.Never);

        _bookingSagaServiceMock.Verify(i => 
            i.ApproveAsync(It.IsAny<ApproveSagaRequest>()), Times.Once);

        _bookingSagaServiceMock.Verify(i => 
            i.ChangeStateAsync(It.IsAny<string>(), It.Is<BookingSagaState>(j => j == BookingSagaState.Completed)), Times.Never);

        _metricsServiceMock.Verify(i => 
            i.DecrementActiveBookingSagaCounter(It.IsAny<string>()), Times.Never);
        
        _brokenReservationServiceMock.Verify(i => 
            i.ReservationBroken(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<Messages.Commands.Models.User>()), Times.Once);
    }
    
    
    [Fact]
    public void HasNonVirtualReservationsToApprove_ExpectReturnFalse_WhenArrayIsEmpty()
    {
        // Arrange
        var reservations = Array.Empty<Reservation>();
        
        // Act
        var result = _service.HasNonVirtualReservationsToApprove(reservations);
        
        // Assert
        result.Should().BeFalse();
    }
    
    [Fact]
    public void HasNonVirtualReservationsToApprove_ExpectReturnFalse_WhenArrayDoesNotContainApprovalRequiredReservations()
    {
        // Arrange
        var nonVirtualReservation = _fixture.Create<Reservation>();
        nonVirtualReservation.IsVirtual = false;
        nonVirtualReservation.ApprovalRequired = false;
        
        var virtualReservation = _fixture.Create<Reservation>();
        virtualReservation.IsVirtual = true;
        virtualReservation.ApprovalRequired = false;
        
        var reservations = new[] { nonVirtualReservation, virtualReservation };
        
        // Act
        var result = _service.HasNonVirtualReservationsToApprove(reservations);
        
        // Assert
        result.Should().BeFalse();
    }
    
    [Fact]
    public void HasNonVirtualReservationsToApprove_ExpectReturnFalse_WhenArrayContainsVirtualApprovalRequiredReservations()
    {
        // Arrange
        var virtualReservation = _fixture.Create<Reservation>();
        virtualReservation.IsVirtual = true;
        virtualReservation.ApprovalRequired = true;
        
        var reservations = new[] { virtualReservation };
        
        // Act
        var result = _service.HasNonVirtualReservationsToApprove(reservations);
        
        // Assert
        result.Should().BeFalse();
    }
    
    [Fact]
    public void HasNonVirtualReservationsToApprove_ExpectReturnTrue_WhenArrayContainsNonVirtualApprovalRequiredReservations()
    {
        // Arrange
        var nonVirtualReservation = _fixture.Create<Reservation>();
        nonVirtualReservation.IsVirtual = false;
        nonVirtualReservation.ApprovalRequired = true;
        
        var reservations = new[] { nonVirtualReservation };
        
        // Act
        var result = _service.HasNonVirtualReservationsToApprove(reservations);
        
        // Assert
        result.Should().BeTrue();
    }
}