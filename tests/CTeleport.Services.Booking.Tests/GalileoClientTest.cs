using CTeleport.Services.Booking.Clients;
using CTeleport.Services.CheckFare.Travelport;
using System;
using System.Collections.Generic;
using System.Text;
using Xunit;

namespace CTeleport.Services.Booking.Tests
{
    public class GalileoClientTest
    {
        [Fact]
        public void InitBookingClientsTest()
        {
            var client = new TravelportCheckFareService(new Galileo.Configuration.GalileoOptions() { Url = "http://localhost" }, null);
            Assert.NotNull(client);
        }
    }
}
