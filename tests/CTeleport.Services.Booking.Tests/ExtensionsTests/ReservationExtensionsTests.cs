using System.Collections.Generic;
using CTeleport.Services.Booking.Extensions;
using CTeleport.Services.Booking.Models;
using Xunit;

namespace CTeleport.Services.Booking.Tests.ExtensionsTests;

public class ReservationExtensionsTests
{
    [Fact]
    public void SumTargetReissueFeesTest()
    {
        // Arrange
        var reservation = new Reservation
        {
            Price = new ReservationPrice
            {
                Currency = "EUR"
            },
            Changes = new List<TicketChanges>
            {
                new()
                {
                    Price = new ChangePrice
                    {
                        TargetReissueFee = 1.11m
                    }
                },
                new()
                {
                    Price = new ChangePrice
                    {
                        TargetReissueFee = 2.22m
                    }
                }
            }
        };

        // Act
        var result = reservation.SumTargetReissueFee();

        // Assert
        Assert.Equal(new Money.Money(3.33m, reservation.Price.Currency), result);
    }

    [Fact]
    public void SumNotRefundedTaxesTest()
    {
        // Arrange
        var reservation = new Reservation
        {
            Price = new ReservationPrice
            {
                OriginalCurrency = "EUR"
            },
            Tickets = new List<Ticket>
            {
                new()
                {
                    Number = "1",
                    Price = new TicketPrice
                    {
                        Taxes = new Dictionary<string, decimal>
                        {
                            { "tax1", 10 },
                            { "tax2", 20.34m }
                        }
                    }
                },
                new()
                {
                    Number = "2",
                    ReissuedFrom = "1",
                    Price = new TicketPrice
                    {
                        Taxes = new Dictionary<string, decimal>
                        {
                            { "tax3", 5 },
                            { "tax4", 30.11m }
                        }
                    }
                }
            }
        };

        var notRefundedTaxes = new Dictionary<string, decimal>
        {
            { "tax1", 5 },
            { "tax3", 0 }
        };
        
        // Act
        var result = reservation.SumNotRefundedTaxes("2", notRefundedTaxes);

        // Assert
        Assert.Equal(new Money.Money(10, reservation.Price.OriginalCurrency), result);
    }

    [Fact]
    public void SumNotRefundedTaxesTest_WhenEmpty()
    {
        // Arrange
        var reservation = new Reservation
        {
            Price = new ReservationPrice
            {
                OriginalCurrency = "EUR"
            }
        };
        
        // Act
        var result = reservation.SumNotRefundedTaxes("2", new Dictionary<string, decimal>());

        // Assert
        Assert.Equal(new Money.Money(0, reservation.Price.OriginalCurrency), result);
    }

    [Fact]
    public void SumNotRefundedTaxesTest_WhenNull()
    {
        // Arrange
        var reservation = new Reservation
        {
            Price = new ReservationPrice
            {
                OriginalCurrency = "EUR"
            }
        };
        
        // Act
        var result = reservation.SumNotRefundedTaxes("2", null);

        // Assert
        Assert.Equal(new Money.Money(0, reservation.Price.OriginalCurrency), result);
    }

    [Fact]
    public void GetFundingSource_ShouldReturnFundingSource_WhenReservationIsTicketless()
    {
        const string fundingSource = "ticketless-funding-source";
       
        var reservation = new Reservation
        {
            Ticketless = true,
            FundingSource = fundingSource
        };
        
        var result = reservation.GetFundingSource();
        
        Assert.Equal(fundingSource, result);
    }
    
    [Fact]
    public void GetFundingSource_ShouldReturnFundingSource_WhenTicketFound()
    {
        const string fundingSource = "ticket-funding-source";
        const string ticketNumber = "123";
        
        var reservation = new Reservation
        {
            Ticketless = false,
            Tickets = new List<Ticket>
            {
                new() 
                { 
                    Number = ticketNumber, 
                    FundingSource = fundingSource 
                }
            }
        };
        
        var result = reservation.GetFundingSource(ticketNumber);
        
        Assert.Equal(fundingSource, result);
    }
}