using AutoFixture;
using CTeleport.Services.Booking.Services.ClassDrop;
using CTeleport.Services.Booking.Services.Interfaces;
using Moq;
using Xunit;

namespace CTeleport.Services.Booking.Tests.ClassDrop;

public class ClassDropVerifierTests
{
    private readonly IFixture _fixture;

    public ClassDropVerifierTests()
    {
        _fixture = new Fixture();
    }
    
    [Fact]
    public void IsPossible_AllOfChecks_True()
    {
        var request = _fixture.Create<ClassDropPossibilityRequest>();

        var check1 = new Mock<IClassDropCheck>(MockBehavior.Strict);
        var check2 = new Mock<IClassDropCheck>(MockBehavior.Strict);

        check1
            .Setup(c => c.IsPossible(It.IsAny<ClassDropPossibilityRequest>(), out It.Ref<string>.IsAny))
            .Returns(true)
            .Verifiable();
        
        check2
            .Setup(c => c.IsPossible(It.IsAny<ClassDropPossibilityRequest>(), out It.Ref<string>.IsAny))
            .Returns(true)
            .Verifiable();
        
        var target = new ClassDropVerifier([check1.Object, check2.Object]);
        
        var actual = target.IsPossible(request, out var notPossibleReason);

        Assert.True(actual);
        Assert.Null(notPossibleReason);
        check1.Verify();
        check2.Verify();
    }
    
    [Fact]
    public void IsPossible_OneOfChecksReturnsFalse_False()
    {
        var request = _fixture.Create<ClassDropPossibilityRequest>();

        var check1 = new Mock<IClassDropCheck>(MockBehavior.Strict);
        var check2 = new Mock<IClassDropCheck>(MockBehavior.Strict);

        check1
            .Setup(c => c.IsPossible(It.IsAny<ClassDropPossibilityRequest>(), out It.Ref<string>.IsAny))
            .Returns(true)
            .Verifiable();
        
        check2
            .Setup(c => c.IsPossible(It.IsAny<ClassDropPossibilityRequest>(), out It.Ref<string>.IsAny))
            .Callback(void (ClassDropPossibilityRequest _, out string reason) => reason = "ABC")
            .Returns(false)
            .Verifiable();
        
        var target = new ClassDropVerifier([check1.Object, check2.Object]);
        
        var actual = target.IsPossible(request, out var notPossibleReason);

        Assert.False(actual);
        Assert.Equal("ABC", notPossibleReason);
        check1.Verify();
        check2.Verify();
    }
    
    [Fact]
    public void IsPossible_OldLogicMet_True()
    {
        var request = new ClassDropPossibilityRequest
        {
            AreBookingCodesIdentical = false,
            AreFaresIdentical = false,
            IsPriceLower = true,
            CabinClassesAreEqual = true,
            BaggageAreEqual = true,
            FareRulesAreEquals = true
        };

        var target = new ClassDropVerifier([
            new BookingCodesIdenticalCheck(),
            new FaresIdenticalCheck(),
            new PriceLowerCheck(),
            new CabinClassesEqualCheck(),
            new BaggageEqualCheck(),
            new FareRulesEqualCheck(),
        ]);
        
        var actual = target.IsPossible(request, out _);
        
        var oldLogic = IsPossible(
            request.AreBookingCodesIdentical, 
            request.AreFaresIdentical,
            request.IsPriceLower,
            request.CabinClassesAreEqual,
            request.BaggageAreEqual,
            request.FareRulesAreEquals);
        
        Assert.True(actual);
        Assert.True(oldLogic);
    }
    
    private static bool IsPossible(bool areNewBookingCodesIdentical, bool isNewFareIdentical, bool isNewPriceLower,
        bool cabinClassesAreEqual, bool baggagesAreEqual, bool fareRulesAreEquals)
    {
        // Old logic copied as-is
            
        var isPossible = !areNewBookingCodesIdentical &&
                         !isNewFareIdentical &&
                         isNewPriceLower &&
                         cabinClassesAreEqual &&
                         baggagesAreEqual &&
                         fareRulesAreEquals;

        return isPossible;
    }
}