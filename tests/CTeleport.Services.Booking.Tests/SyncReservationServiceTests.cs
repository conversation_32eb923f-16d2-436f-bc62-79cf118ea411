using AutoMapper;
using CTeleport.Common.Exceptions;
using CTeleport.Common.Helpers;
using CTeleport.Messages.Commands;
using CTeleport.Messages.Commands.Reservations;
using CTeleport.Services.Booking.Core.Dto;
using CTeleport.Services.Booking.Core.Interfaces;
using CTeleport.Services.Booking.Enums;
using CTeleport.Services.Booking.Models;
using CTeleport.Services.Booking.Repositories;
using CTeleport.Services.Booking.Services;
using CTeleport.Services.Booking.Services.Interfaces;
using CTeleport.Services.Booking.Shared.Models;
using CTeleport.Services.Helpers;
using CTeleport.Services.Search.Shared.Enums;
using CTeleport.Services.Search.Shared.Models;
using Moq;
using Serilog;
using ServiceStack.Text;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CTeleport.Services.Booking.Api;
using Xunit;
using User = CTeleport.Messages.Commands.Models.User;

namespace CTeleport.Services.Booking.Tests
{
    public class SyncReservationServiceTests
    {
        private readonly MockRepository _mockRepository;

        private readonly Mock<ILegRecalculationService> _legRecalculationServiceMock;
        private readonly Mock<IReservationsRepository> _mockReservationRepository;
        private readonly Mock<IBookingMetricsService> _bookingMetricsServiceMock;
        private readonly Mock<IFareChangeService> _fareChangeServiceMock;
        private readonly Mock<IBookingService> _bookingServiceMock;
        private readonly Mock<IMapper> _mapperMock;
        private readonly Mock<ILogger> _loggerMock;
        
        private static readonly Dictionary<string, string> Locators = new Dictionary<string, string>
            {{LocatorNames.PROVIDER, string.Empty}};

        private static readonly Reservation SYNC_FARE_RESERVATION = new Reservation
        {
            Id = Id.New(),
            Locators = Locators,
            Fare = new Shared.Models.Fare { NonRefAmounts = new NonRefundableAmounts(), Cancellations = RefundCondition.PaidRefund }
        };

        private static readonly Reservation SYNC_SEGMENTS_RESERVATION = new Reservation
        {
            Id = Id.New(),
            Locators = Locators,
            LegSegments = new List<ICollection<Segment>>()
        };

        private static readonly Reservation REISSUED_RESERVATION = new Reservation
        {
            Id = Id.New(),
            Locators = Locators,
            LegSegments = new List<ICollection<Segment>>
            {
                new List<Segment>
                {
                    new Segment
                    {
                        Carrier = "CX",
                        Origin = "AMS",
                        Destination = "HKG",
                        Status = "HK",
                        DepartureDate = "2021-12-18",
                        DepartureTime = "12:30:00.000+00.00",
                        ArrivalDate = "2021-12-19",
                        ArrivalTime = "06:45:00.000+08.00"
                    },
                    new Segment
                    {
                        Carrier = "CX",
                        Origin = "HKG",
                        Destination = "CEB",
                        Status = "HK",
                        DepartureDate = "2021-12-19",
                        DepartureTime = "16:05:00.000+08.00",
                        ArrivalDate = "2021-12-19",
                        ArrivalTime = "18:55:00.000+08.00"
                    }
                }
            }
        };

        private static readonly Reservation MULTILEG_RESERVATION = new Reservation
        {
            Id = Id.New(),
            Locators = Locators,
            LegSegments = new List<ICollection<Segment>>
            {
                new List<Segment>
                {
                    new Segment
                    {
                        Carrier = "CX",
                        Origin = "AMS",
                        Destination = "HKG",
                        Status = "HK",
                        DepartureDate = "2021-12-18",
                        DepartureTime = "12:30:00.000+00.00",
                        ArrivalDate = "2021-12-19",
                        ArrivalTime = "06:45:00.000+08.00"
                    }
                },
                new List<Segment>
                {
                    new Segment
                    {
                        Carrier = "CX",
                        Origin = "HKG",
                        Destination = "AMS",
                        Status = "HK",
                        DepartureDate = "2021-12-21",
                        DepartureTime = "18:05:00.000+08.00",
                        ArrivalDate = "2021-12-21",
                        ArrivalTime = "20:55:00.000+00.00"
                    }
                }
            }
        };

        private static readonly Reservation REISSUE_NOSHOW_PARTLYUSED_RESERVATION =
            TestHelper.GetMockData<Reservation>("Reissue-NoShow-PartlyUsed-Reservation.json");

        public SyncReservationServiceTests()
        {
            _mockRepository = new MockRepository(MockBehavior.Loose);

            _legRecalculationServiceMock = _mockRepository.Create<ILegRecalculationService>();
            _mockReservationRepository = _mockRepository.Create<IReservationsRepository>();
            _bookingMetricsServiceMock = _mockRepository.Create<IBookingMetricsService>();
            _fareChangeServiceMock = _mockRepository.Create<IFareChangeService>();
            _bookingServiceMock = _mockRepository.Create<IBookingService>();
            _mapperMock = _mockRepository.Create<IMapper>();
            _loggerMock = _mockRepository.Create<ILogger>();

            _loggerMock
                .Setup(logger => logger.ForContext(It.IsAny<string>(), It.IsAny<object>(), It.IsAny<bool>()))
                .Returns(() => _loggerMock.Object);

            InitBookingServiceMock(
                SYNC_FARE_RESERVATION,
                SYNC_SEGMENTS_RESERVATION,
                REISSUED_RESERVATION,
                MULTILEG_RESERVATION,
                REISSUE_NOSHOW_PARTLYUSED_RESERVATION);
        }

        [Fact]
        public async Task Reissued_Reservation()
        {
            // Arrange
            var reservationInfoServiceMock = CreateReservationInfoServiceMock(
                null,
                () => new List<Segment>
                {
                    new Segment
                    {
                        Carrier = "BA",
                        ConnectionDuration = 210,
                        Origin = "AMS",
                        Destination = "LHR",
                        Status = "HK",
                        DepartureDate = "2021-12-18",
                        DepartureTime = "07:30:00.000+00.00",
                        ArrivalDate = "2021-12-18",
                        ArrivalTime = "07:45:00.000+00.00"
                    },
                    new Segment
                    {
                        Carrier = "CX",
                        Origin = "LHR",
                        Destination = "HKG",
                        Status = "HK",
                        DepartureDate = "2021-12-18",
                        DepartureTime = "11:15:00.000+00.00",
                        ArrivalDate = "2021-12-19",
                        ArrivalTime = "07:00:00.000+08.00"
                    },
                    new Segment
                    {
                        Carrier = "CX",
                        Origin = "HKG",
                        Destination = "CEB",
                        Status = "HK",
                        DepartureDate = "2021-12-19",
                        DepartureTime = "16:05:00.000+08.00",
                        ArrivalDate = "2021-12-19",
                        ArrivalTime = "18:55:00.000+08.00"
                    }
                });
            
            var service = CreateSyncReservationService(reservationInfoServiceMock.Object, AutoMapperConfig.InitializeMapper());
            var command = CreateSyncReservationCommand(Id.New(), REISSUED_RESERVATION.Id, false, true);
            command.ForceSegments = true;

            // Act & Assert
            await service.SyncReservation(command.ReservationId, command.ForceSegments, command.SyncSegments, command.SyncFare, false, FareChangeReason.FareUpdate, It.IsAny<string>());
        }

        [Fact]
        public async Task ForceSegments_Reissued_NoShow_PartlyUsed_Reservation()
        {
            // Arrange
            var reservationInfoServiceMock = CreateReservationInfoServiceMock(
                null,
                () => new List<Segment>
                {
                    new Segment
                    {
                        Carrier = "EY",
                        CabinClass = "Economy",
                        FlightNumber = "474",
                        Origin = "AUH",
                        Destination = "CGK",
                        DepartureDate = "2021-06-09",
                        DepartureTime = "01:55:00.000+04:00",
                        ArrivalDate = "2021-06-09",
                        ArrivalTime = "13:10:00.000+07:00",
                        Status = "NS",
                        BookingCode = "L"
                    },
                    new Segment
                    {
                        Carrier = "EY",
                        CabinClass = "Economy",
                        FlightNumber = "474",
                        Origin = "CGK",
                        Destination = "AUH",
                        DepartureDate = "2021-09-30",
                        DepartureTime = "23:10:00.000+07:00",
                        ArrivalDate = "2021-10-01",
                        ArrivalTime = "03:55:00.000+04:00",
                        Status = "HK",
                        BookingCode = "L"
                    },
                    new Segment
                    {
                        Carrier = "EY",
                        CabinClass = "Economy",
                        FlightNumber = "474",
                        Origin = "CGK",
                        Destination = "AUH",
                        DepartureDate = "2021-11-09",
                        DepartureTime = "22:50:00.000+07:00",
                        ArrivalDate = "2021-11-10",
                        ArrivalTime = "03:55:00.000+04:00",
                        Status = "HK",
                        BookingCode = "L"
                    },
                });

            var service = CreateSyncReservationService(reservationInfoServiceMock.Object, AutoMapperConfig.InitializeMapper());
            var command = CreateSyncReservationCommand(Id.New(), REISSUE_NOSHOW_PARTLYUSED_RESERVATION.Id, false, true);
            command.ForceSegments = true;

            // Act & Assert
            await service.SyncReservation(command.ReservationId, command.ForceSegments, command.SyncSegments, command.SyncFare, false, FareChangeReason.FareUpdate, It.IsAny<string>());
        }

        [Fact]
        public async Task ForceSegments_UsedSegmentNotDisplayed()
        {
            // Arrange
            var reservationInfoServiceMock = CreateReservationInfoServiceMock(
                null,
                () => new List<Segment>
                {
                    new Segment
                    {
                        Carrier = "EY",
                        CabinClass = "Economy",
                        FlightNumber = "474",
                        Origin = "CGK",
                        Destination = "AUH",
                        DepartureDate = "2021-11-09",
                        DepartureTime = "22:50:00.000+07:00",
                        ArrivalDate = "2021-11-10",
                        ArrivalTime = "03:55:00.000+04:00",
                        Status = "HK",
                        BookingCode = "L"
                    },
                });

            var service = CreateSyncReservationService(reservationInfoServiceMock.Object, AutoMapperConfig.InitializeMapper());
            var command = CreateSyncReservationCommand(Id.New(), REISSUE_NOSHOW_PARTLYUSED_RESERVATION.Id, false, true);
            command.ForceSegments = true;

            // Act & Assert
            await service.SyncReservation(command.ReservationId, command.ForceSegments, command.SyncSegments, command.SyncFare, false, FareChangeReason.FareUpdate, It.IsAny<string>());
        }

        [Fact]
        public async Task ForceSegments_Multileg_Sync()
        {
            // Arrange
            var reservationInfoServiceMock = CreateReservationInfoServiceMock(
                null,
                () => new List<Segment>
                {
                    new Segment
                    {
                        Carrier = "BA",
                        ConnectionDuration = 210,
                        Origin = "AMS",
                        Destination = "LHR",
                        Status = "HK",
                        DepartureDate = "2021-12-18",
                        DepartureTime = "07:30:00.000+00.00",
                        ArrivalDate = "2021-12-18",
                        ArrivalTime = "07:45:00.000+00.00",
                        Connecting = true
                    },
                    new Segment
                    {
                        Carrier = "CX",
                        Origin = "LHR",
                        Destination = "HKG",
                        Status = "HK",
                        DepartureDate = "2021-12-18",
                        DepartureTime = "11:15:00.000+00.00",
                        ArrivalDate = "2021-12-19",
                        ArrivalTime = "07:00:00.000+08.00"
                    },
                    new Segment
                    {
                        Carrier = "CX",
                        Origin = "HKG",
                        Destination = "AMS",
                        Status = "HK",
                        DepartureDate = "2021-12-21",
                        DepartureTime = "18:05:00.000+08.00",
                        ArrivalDate = "2021-12-21",
                        ArrivalTime = "20:55:00.000+00.00"
                    }
                });

            var service = CreateSyncReservationService(reservationInfoServiceMock.Object, AutoMapperConfig.InitializeMapper());
            var command = CreateSyncReservationCommand(Id.New(), MULTILEG_RESERVATION.Id, false, true);
            command.ForceSegments = true;

            // Act & Assert
            await service.SyncReservation(command.ReservationId, command.ForceSegments, command.SyncSegments, command.SyncFare, false, FareChangeReason.FareUpdate, It.IsAny<string>());
        }

        [Fact]
        public async Task ForceSegments_LegODChange()
        {
            // Arrange
            var reservationInfoServiceMock = CreateReservationInfoServiceMock(
                null,
                () => new List<Segment>
                {
                    new Segment
                    {
                        Carrier = "CX",
                        Origin = "AMS",
                        Destination = "HKG",
                        Status = "HK",
                        DepartureDate = "2021-12-18",
                        DepartureTime = "12:30:00.000+00.00",
                        ArrivalDate = "2021-12-19",
                        ArrivalTime = "06:45:00.000+08.00"
                    },
                    new Segment
                    {
                        Carrier = "CX",
                        Origin = "HKG",
                        Destination = "MNL",
                        Status = "HK",
                        DepartureDate = "2021-12-19",
                        DepartureTime = "16:05:00.000+08.00",
                        ArrivalDate = "2021-12-19",
                        ArrivalTime = "18:55:00.000+08.00"
                    }
                });

            var service = CreateSyncReservationService(reservationInfoServiceMock.Object, AutoMapperConfig.InitializeMapper());
            var command = CreateSyncReservationCommand(Id.New(), REISSUED_RESERVATION.Id, false, true);
            command.ForceSegments = true;

            // Act & Assert
            await service.SyncReservation(command.ReservationId, command.ForceSegments, command.SyncSegments, command.SyncFare, false, FareChangeReason.FareUpdate, It.IsAny<string>());
        }

        [Fact]
        public void Exception_Unknown_Reservation()
        {
            //arrange
            var reservationInfoServiceMock = CreateReservationInfoServiceMock();
            var service = CreateSyncReservationService(reservationInfoServiceMock.Object);
            var command = CreateSyncReservationCommand(Id.New(), Id.New());

            //act
            Assert.ThrowsAsync<ValidationException>(() 
                => service.SyncReservation(command.ReservationId, command.ForceSegments, command.SyncSegments, command.SyncFare, false, FareChangeReason.FareUpdate, It.IsAny<string>()));
        }

        [Fact]
        public async void Success_SyncFare_Reservation()
        {
            //arrange
            var reservationInfoServiceMock = CreateReservationInfoServiceMock(initFare: () => SetupMapperMock<Shared.Models.Fare>());
            var service = CreateSyncReservationService(reservationInfoServiceMock.Object);
            var command = CreateSyncReservationCommand(Id.New(), SYNC_FARE_RESERVATION.Id, syncFare: true);

            //act
            await service.SyncReservation(command.ReservationId, command.ForceSegments, command.SyncSegments, command.SyncFare, false, FareChangeReason.FareUpdate, It.IsAny<string>());
        }
        
        [Fact]
        public async void Success_SyncSegments_Reservation()
        {
            //arrange
            var segments = GetSegments();
            segments.ForEach(segment => SetupMapperMock(segment));
            SYNC_SEGMENTS_RESERVATION.LegSegments = new List<ICollection<Segment>> { segments };
            var reservationInfoServiceMock = CreateReservationInfoServiceMock(initSegment: () => segments);
            var service = CreateSyncReservationService(reservationInfoServiceMock.Object);
            var command = CreateSyncReservationCommand(Id.New(), SYNC_SEGMENTS_RESERVATION.Id, syncSegments: true);

            //act
            await service.SyncReservation(command.ReservationId, command.ForceSegments, command.SyncSegments, command.SyncFare, false, FareChangeReason.FareUpdate, It.IsAny<string>());
        }

        [Fact]
        public async void Check_call_of_UpdateReservationSegmentsAsync_for_SyncSegments_Reservation()
        {
            //arrange
            var segments = GetSegments();
            segments.ForEach(segment => SetupMapperMock(segment));
            SYNC_SEGMENTS_RESERVATION.LegSegments = new List<ICollection<Segment>> { segments };
            var reservationInfoServiceMock = CreateReservationInfoServiceMock(initSegment: () => segments);
            var service = CreateSyncReservationService(reservationInfoServiceMock.Object);
            var command = CreateSyncReservationCommand(Id.New(), SYNC_SEGMENTS_RESERVATION.Id, syncSegments: true);

            //act
            await service.SyncReservation(command.ReservationId, command.ForceSegments, command.SyncSegments, command.SyncFare, false, FareChangeReason.FareUpdate, It.IsAny<string>());

            //assert
            _bookingServiceMock.Verify(b => b.UpdateReservationSegmentsAsync(
                It.Is<Reservation>(r => r.LegSegments.First().SequenceEqual(segments) && r.DepartureAt == r.LegSegments.GetDepartureAt())), Times.Exactly(1));
        }

        [Fact]
        public void Exception_SourceSegmentNotFound_Segments_SyncSegments()
        {
            //arrange
            var segments = GetSegments();
            segments.ForEach(segment => SetupMapperMock(segment));
            SYNC_SEGMENTS_RESERVATION.LegSegments = new List<ICollection<Segment>> { segments };
            var reservationInfoServiceMock = CreateReservationInfoServiceMock(initSegment: () => new List<Segment>());
            var service = CreateSyncReservationService(reservationInfoServiceMock.Object);
            var command = CreateSyncReservationCommand(Id.New(), SYNC_SEGMENTS_RESERVATION.Id, syncSegments: true);

            //act
            Assert.ThrowsAsync<ValidationException>(() 
                => service.SyncReservation(command.ReservationId, command.ForceSegments, command.SyncSegments, command.SyncFare, false, FareChangeReason.FareUpdate, It.IsAny<string>()));
        }

        #region helpers

        private Mock<IProviderReservationInfoService> CreateReservationInfoServiceMock(Func<Shared.Models.Fare> initFare = null, Func<IList<Segment>> initSegment = null)
        {
            var reservationInfoServiceMock = new Mock<IProviderReservationInfoService>();


            reservationInfoServiceMock
                .Setup(r => r.GetReservationAsync(It.IsAny<ProviderRetrieveReservationRequest>()))
                .ReturnsAsync(() =>
                {
                    var response = new ProviderRetrieveReservationResponse
                    {
                        FlightReservation = new ProviderFlightReservation { }
                    };
                    if (initFare != null)
                    {
                        response.FlightReservation.Fare = initFare();
                    }
                    if (initSegment != null)
                    {
                        response.FlightReservation.Segments = initSegment();
                    }

                    return response;
                });

            return reservationInfoServiceMock;
        }

        private void InitBookingServiceMock(params Reservation[] reservations)
        {
            foreach (var reservation in reservations)
            {
                _bookingServiceMock
                    .Setup(r => r.GetReservationAsync(reservation.Id))
                    .ReturnsAsync(() => reservation);

                _bookingServiceMock
                    .Setup(r => r.UpdateReservationFareAsync(reservation, It.IsAny<Shared.Models.Fare>(), It.IsAny<FareChangeReason>()))
                    .Returns(Task.CompletedTask);

                _bookingServiceMock
                    .Setup(r => r.UpdateReservationSegmentsAsync(reservation))
                    .Returns(Task.CompletedTask);

                _bookingServiceMock
                    .Setup(r => r.GetBookingAsync(It.IsAny<string>()))
                    .ReturnsAsync(() => new Models.Booking());
            }
        }

        private List<Segment> GetSegments()
        {
            return new List<Segment>
            {
                new Segment
                {
                    Status = "HK",
                    Origin = "IAH",
                    Destination = "IST",
                    Carrier = "TK",
                    FlightNumber = "34",
                    DepartureDate = "2018-06-26",
                    DepartureTime = "21:05",
                    DepartureTimestamp = 1530047100,
                    DepartureTimestampUtc = 1530065100,
                    ArrivalDate = "2018-06-27",
                    ArrivalTime = "16:55",
                    ArrivalTimestamp = 1530118500,
                    ArrivalTimestampUtc = 1530129300
                },
                new Segment
                {
                    Status = "HK",
                    Origin = "IST",
                    Destination = "KHE",
                    Carrier = "TK",
                    FlightNumber = "471",
                    DepartureDate = "2018-06-28",
                    DepartureTime = "06:45",
                    DepartureTimestamp = **********,
                    DepartureTimestampUtc = **********,
                    ArrivalDate = "2018-06-28",
                    ArrivalTime = "08:15",
                    ArrivalTimestamp = **********,
                    ArrivalTimestampUtc = **********
                }
            };
        }

        private SyncReservationService CreateSyncReservationService(
            IProviderReservationInfoService providerReservationInfoService,
            IMapper mapper = null)
        {
            return new SyncReservationService(
                providerReservationInfoService,
                _legRecalculationServiceMock.Object,
                new SegmentSyncService(_loggerMock.Object, mapper ?? _mapperMock.Object),
                _bookingMetricsServiceMock.Object,
                _fareChangeServiceMock.Object,
                _bookingServiceMock.Object,
                _loggerMock.Object);
        }

        private T SetupMapperMock<T>(T obj = null) where T : class, new()
        {
            obj = obj ?? new T();
            _mapperMock.Setup(m => m.Map<T>(obj))
                .Returns(() => obj);
            return obj;
        }

        private SyncReservation CreateSyncReservationCommand(string requestId, string reservationId, bool syncFare = false, bool syncSegments = false)
            => new()
            {
                Request = Request.New<SyncReservation>(requestId),
                ReservationId = reservationId,
                User = new User(),
                SyncFare = syncFare,
                SyncSegments = syncSegments
            };

        #endregion
    }

    internal static class SegmentCollectionExt
    {
        internal static DateTime GetDepartureAt(this ICollection<ICollection<Segment>> segments)
        {
            return segments.First().First().DepartureTimestampUtc.FromUnixTime();
        }
    }
}
