using System.Collections.Generic;
using System.Threading.Tasks;
using CTeleport.Services.Billing.Clients;
using CTeleport.Services.Price.Client;
using CTeleport.Services.Price.Models;
using CTeleport.Services.Price.Services;
using CTeleport.Services.Price.Shared.Models;
using CTeleport.Services.Settings.Clients;
using CTeleport.Services.Settings.Contracts.TenantManagementService;
using CTeleport.Services.TenantShared.Models;
using Moq;
using Xunit;

namespace CTeleport.Services.Booking.Tests;

public class PriceServiceTests
{
    private readonly PriceService _service;
    
    private readonly Mock<IPriceClient> _priceClientMock;

    public PriceServiceTests()
    {
        _priceClientMock = new Mock<IPriceClient>();
        var billingClientMock = new Mock<IBillingClient>();
        var tenantManagementService = new Mock<ITenantManagementServiceClient>();

        billingClientMock.Setup(c => c.GetTenantBillingPreferencesAsync(It.IsAny<string>()))
            .ReturnsAsync(() => new TenantBillingPreferencesDto());

        tenantManagementService.Setup(c => c.GetTenantAsync(It.IsAny<string>(), It.IsAny<bool>()))
            .ReturnsAsync(() => new Tenant()
            {
                Kickback = new Kickback
                {
                    Currency = "USD"
                }
            });
            
        _service = new PriceService(_priceClientMock.Object, 
            billingClientMock.Object, 
            tenantManagementService.Object);
    }

    [Fact]
    public async Task CreateConverterFromReservationPrice_UseExistingCurrencyRate()
    {
        //Arrange
        var tenantId = "Tenant";

        var reservationPriceDto = new ReservationPriceDto
        {
            Currency = "USD",
            MarkupCurrency = "USD",
            ConversionRates = new Dictionary<string, decimal>
            {
                ["USD-EUR"]= 5.0m,
                ["EUR-USD"]= 0.2m,
            },
            ConversionMarginRates = new Dictionary<string, decimal>
            {
                ["USD-EUR"]= 1.25m,
                ["EUR-USD"]= 0.8m,
            }
        };
        
        //Act
        var converter = await _service.CreatePriceConverterAsync(tenantId, reservationPriceDto);
        
        var priceCalcResult = converter.CalculateChangeFee(100m, "EUR");
        
        //Assert
        Assert.Equal(36, priceCalcResult.TotalPrice);
    }
    
    [Fact]
    public async Task SameCurrency_PriceNotChanges()
    {
        //Arrange
        var tenantId = "Tenant";

        var reservationPriceDto = new ReservationPriceDto
        {
            Currency = "USD",
            MarkupCurrency = "USD",
            ConversionRates = new Dictionary<string, decimal>
            {
                ["USD-USD"]= 1.0m,
            },
            ConversionMarginRates = new Dictionary<string, decimal>
            {
                ["USD-USD"]= 0.0m,
            }
        };
        
        //Act
        var converter = await _service.CreatePriceConverterAsync(tenantId, reservationPriceDto);
        
        var priceCalcResult = converter.CalculateChangeFee(100m, "USD");
        
        //Assert
        Assert.Equal(100m, priceCalcResult.TotalPrice);
    }

    [Fact]
    public async Task CreateConverter_UseCurrentCurrencyRate()
    {
        //Arrange
        var tenantId = "Tenant";

        _priceClientMock.Setup(c => c.GetPriceConverterSettings(tenantId, "Vessel", "EUR", "USD", "1G"))
            .ReturnsAsync(() => new PriceConverterSettings
                {
                    Markup = 0,
                    MarkupCcy = "USD",
                    Kickback = new Kickback
                    {
                        Currency = "USD"
                    },
                    CompensationMarkup = 0,
                    ConsolidatorMarkup = 0,
                    ConvMarginsRates = new Dictionary<string, decimal>
                    {
                        ["USD-EUR"] = 1.25m,
                        ["EUR-USD"] = 0.8m,
                    },
                    ConvRates = new Dictionary<string, decimal>
                    {
                        ["USD-EUR"] = 5.0m,
                        ["EUR-USD"] = 0.2m,
                    },
                    TargetCcy = "USD",
                    MarineFareMarkup = new MarineFareMarkup(),
                    HighFareMarkup = new HighFareMarkup(),
                    RoundTotalPrice = false
                }
            );
        
        //Act
        var converter = await _service.CreatePriceConverterAsync(tenantId, "Vessel", "EUR", "USD", "1G");
        
        var priceCalcResult = converter.CalculateChangeFee(100m, "EUR");
        
        //Assert
        Assert.Equal(36, priceCalcResult.TotalPrice);
    }
}