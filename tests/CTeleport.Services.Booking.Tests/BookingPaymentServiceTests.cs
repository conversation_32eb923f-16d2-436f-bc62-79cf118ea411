using System;
using System.Linq;
using System.Threading.Tasks;
using AutoFixture;
using CTeleport.Services.Booking.Clients;
using CTeleport.Services.Booking.Sagas;
using CTeleport.Services.Booking.Services;
using FluentAssertions;
using Moq;
using Serilog.Core;
using Xunit;
using BookingSaga = CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Booking;

namespace CTeleport.Services.Booking.Tests
{
    public class BookingPaymentServiceTests
    {
        private readonly Fixture _fixture;
        private readonly BookingPaymentService _target;
        private readonly Mock<IBillingClient> _billingClient;

        public BookingPaymentServiceTests()
        {
            _fixture = new Fixture();
            _fixture.Customize<DateOnly>(composer => composer.FromFactory<DateTime>(DateOnly.FromDateTime));
            
            _billingClient = new Mock<IBillingClient>(MockBehavior.Strict);
            _target = new BookingPaymentService(Mock.Of<IBookingService>(), _billingClient.Object, Logger.None);
        }

        [Fact]
        public async Task MakeCreditCardPaymentAsync_Ancillaries_CreditCardPaymentRequestContainsBookingTotalPlusAncillariesWithTheirMarkup()
        {
            var createBookingSaga = _fixture.Create<BookingSaga>();

            var ancillariesTotal = createBookingSaga.Reservations
                .SelectMany(r => r.Ancillaries.Select(a => a.Price + a.Markup)).Sum();

            var total = createBookingSaga.BookingEntity.Price.Total + ancillariesTotal;

            _billingClient
                .Setup(c => c.MakeCreditCardPaymentAsync(It.IsAny<MakeCreditCardPaymentRequest>()))
                .ReturnsAsync(_fixture.Create<CreditCardPaymentDto>());

            var actual = await _target.CreatePaymentAsync(createBookingSaga);

            actual.Id.Should().NotBeNullOrWhiteSpace();

            _billingClient.Verify(
                c => c.MakeCreditCardPaymentAsync(It.Is<MakeCreditCardPaymentRequest>(r => r.Amount == total)),
                Times.Once);
        }

        [Fact]
        public async Task MakeCreditCardPaymentAsync_NoAncillaries_CreditCardPaymentRequestContainsBookingTotal()
        {
            var bookingReservationsWithoutAnyAncillaries = _fixture.Build<CreateBooking.BookingReservation>()
                .Without(b => b.SelectedAncillaries)
                .CreateMany()
                .ToList();

            var createBookingSaga = _fixture.Build<BookingSaga>()
                //.With(b => b.Reservations, bookingReservationsWithoutAnyAncillaries)
                .Create();

            _billingClient
                .Setup(c => c.MakeCreditCardPaymentAsync(It.IsAny<MakeCreditCardPaymentRequest>()))
                .ReturnsAsync(_fixture.Create<CreditCardPaymentDto>());

            var actual = await _target.CreatePaymentAsync(createBookingSaga);

            actual.Id.Should().NotBeNullOrWhiteSpace();

            _billingClient.Verify(
                c => c.MakeCreditCardPaymentAsync(It.Is<MakeCreditCardPaymentRequest>(r => r.Amount == createBookingSaga.BookingEntity.Price.Total)),
                Times.Once);
        }
    }
}