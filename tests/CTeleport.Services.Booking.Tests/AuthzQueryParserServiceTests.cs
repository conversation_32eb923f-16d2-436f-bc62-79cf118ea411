using System.Collections.Generic;
using System.Linq;
using CTeleport.Services.Booking.Clients;
using CTeleport.Services.Booking.Clients.Authz;
using CTeleport.Services.Booking.Services;
using FluentAssertions;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using Xunit;

namespace CTeleport.Services.Booking.Tests
{
    public class AuthzQueryParserServiceTests
    {
        private IAuthzQueryParserService _queryParserService;

        private static readonly PartialResult EmptyResult = new PartialResult()
        {
            Queries = new List<List<Expr>> {
                new List<Expr> {}
            }
        };

        private static readonly PartialResult PartialResult = new PartialResult()
        {
            Queries = new List<List<Expr>>
            {
                new List<Expr>
                {
                    new Expr
                    {
                        Index = 0,
                        Terms = new List<Term>
                        {
                            new RefTerm { Type = "ref", Value = new List<Term> { new ValueTerm { Type = "var", Value = "eq" } } },
                            new ValueTerm { Type = "string", Value = "cteleport" },
                            new RefTerm { Type = "ref", Value = new List<Term>
                            {
                                new ValueTerm { Type = "var", Value = "input" },
                                new ValueTerm { Type = "string", Value = "resource" },
                                new ValueTerm { Type = "string", Value = "tenant_id" },
                            } }
                        }
                    },
                    new Expr
                    {
                        Index = 1,
                        Terms = new List<Term>
                        {
                            new RefTerm { Type = "ref", Value = new List<Term> { new ValueTerm { Type = "var", Value = "neq" } } },
                            new ValueTerm { Type = "string", Value = "auth0|abcd" },
                            new RefTerm { Type = "ref", Value = new List<Term>
                            {
                                new ValueTerm { Type = "var", Value = "input" },
                                new ValueTerm { Type = "string", Value = "resource" },
                                new ValueTerm { Type = "string", Value = "created_by" },
                                new ValueTerm { Type = "string", Value = "id" }
                            } }
                        }
                    }
                },
                new List<Expr>
                {
                    new Expr
                    {
                        Index = 0,
                        Terms = new List<Term>
                        {
                            new RefTerm { Type = "ref", Value = new List<Term> { new ValueTerm { Type = "var", Value = "neq" } } },
                            new ValueTerm { Type = "string", Value = "auth0|abcd" },
                            new RefTerm { Type = "ref", Value = new List<Term>
                            {
                                new ValueTerm { Type = "var", Value = "input" },
                                new ValueTerm { Type = "string", Value = "resource" },
                                new ValueTerm { Type = "string", Value = "created_by" },
                                new ValueTerm { Type = "string", Value = "id" }
                            } }
                        }
                    }
                }
            }
        };

        public AuthzQueryParserServiceTests()
        {
            _queryParserService = new AuthzQueryParserService();
        }

        [Fact]
        public void Parse_should_return_empty_list()
        {
            var actual = _queryParserService.Parse(EmptyResult);
            actual.Should().HaveCount(0);
        }

        [Fact]
        public void Parse_shouldExtractAllConditionsFromTerms()
        {
            var actual = _queryParserService.Parse(PartialResult);
            actual.Should().HaveCount(2);
        }

        [Fact]
        public void Parse_shouldExtractOperators()
        {
            var actual = _queryParserService.Parse(PartialResult);
            actual.Should().Contain(x => x.First().Operator == AuthzQueryParserService.Operator.Equal)
                .And.Contain(x => x.First().Operator == AuthzQueryParserService.Operator.NotEqual);
        }

        [Fact]
        public void Parse_shouldExtractFieldNames()
        {
            var actual = _queryParserService.Parse(PartialResult);
            actual.Should().Contain(x => x.First().Field == "tenant_id")
                .And.Contain(x => x.First().Field == "created_by.id");
        }

        [Fact]
        public void Parse_shouldExtractValues()
        {
            var actual = _queryParserService.Parse(PartialResult);
            actual.Should().Contain(x => (string)x.First().Value == "cteleport")
                .And.Contain(x => (string)x.First().Value == "auth0|abcd");
        }

        [Fact]
        public void Parse_shouldProcessAllQueries()
        {
            var actual = _queryParserService.Parse(PartialResult);
            actual.Should().HaveCount(2);
        }
    }
}