using System;
using System.Collections.Generic;
using CTeleport.Common.Helpers;
using CTeleport.Services.VoidCalc.Configuration;
using CTeleport.Services.VoidCalc.Interfaces;
using CTeleport.Services.VoidCalc.Services;
using CTeleport.Services.VoidCalc.Services.Carriers;
using Moq;
using NodaTime;
using NodaTime.Extensions;
using Serilog.Core;
using Xunit;

namespace CTeleport.Services.Booking.Tests
{
    public class VoidTimeServiceTwentyFourTests
    {
        static readonly VoidRule VoidRuleTwentyFourHours = VoidRule.NextWorkingDayMidnight;

        static readonly VoidOptions options = new VoidOptions()
        {
            VoidSafetyTime = 60,
            CustomVoidRules = new Dictionary<string, VoidRule>()
            {
                { "1G.55EL", VoidRule.NextWorkingDayMidnight },
                { "1A.FLL1S21DX", VoidRule.SameDayMidnight }
            }
        };

        private readonly string _sourceRix = "1G.55EL";
        private readonly string _sourceUS = "1A.FLL1S21DX";
        private readonly string _sourceAT = "1G.37Y";
        private readonly string _sourceLS = "1G.LSS";
        private readonly string _carrier = "HX";
        private readonly HashSet<string> _marketingCarriers = new HashSet<string> { "HX" };

        static readonly DateTimeZone PCC_TZ = DateTimeZoneProviders.Tzdb["Europe/Riga"];
        static readonly DateTimeZone US_PCC_TZ = DateTimeZoneProviders.Tzdb["America/New_York"];
        static readonly DateTimeZone LS_PCC_TZ = DateTimeZoneProviders.Tzdb["Europe/Lisbon"];
        static readonly DateTimeZone AT_PCC_TZ = DateTimeZoneProviders.Tzdb["Europe/Athens"];
        private readonly Mock<ISourceTimeZoneResolverService> _timezoneResolverMock;
        private readonly VoidTimeService _service;

        public VoidTimeServiceTwentyFourTests()
        {
            _timezoneResolverMock = new Mock<ISourceTimeZoneResolverService>();

            _timezoneResolverMock
                .Setup(r => r.GetSourceTimeZone(_sourceRix))
                .Returns(() => PCC_TZ);

            _timezoneResolverMock
                .Setup(r => r.GetSourceTimeZone(_sourceUS))
                .Returns(() => US_PCC_TZ);

            _timezoneResolverMock
                .Setup(r => r.GetSourceTimeZone(_sourceLS))
                .Returns(() => LS_PCC_TZ);

            _timezoneResolverMock
                .Setup(r => r.GetSourceTimeZone(_sourceAT))
                .Returns(() => AT_PCC_TZ);
            _service = CreateService(options, DateTime.UtcNow);
        }

        private VoidTimeService CreateService(VoidOptions voidOptions, DateTime date)
        {
            var timeMock = new Mock<DateTimeProvider>();
            timeMock.SetupGet(tp => tp.UtcNow).Returns(date);
            var converter = new PccDateTimeConverter(_timezoneResolverMock.Object, timeMock.Object);
            var voidTimeCalculator = new VoidTimeCalculator(voidOptions);
            
            IVoidTimeResolver[] resolvers = 
            [
                new SameDayMidnightVoidTimeResolver(voidTimeCalculator),
                new DayBeforeDepartureResolver(voidTimeCalculator, voidOptions),
                new BeforeDepartureVoidTimeResolver(voidTimeCalculator),
                new AeroflotVoidTimeResolver(voidTimeCalculator),
                new IssueDateTimeAsVoidTimeResolver(),
                new VoidOnDepartureDayNotPermittedResolver(voidTimeCalculator)
            ];
            
            Random.Shared.Shuffle(resolvers);
            
            return new VoidTimeService(_timezoneResolverMock.Object, voidOptions, converter, voidTimeCalculator, resolvers, Logger.None);
        }

        [Fact]
        public void CalcLastVoidTime_Should_Allow_Voiding_Until_Next_Working_Day()
        {
            var issueAt = new DateTime(2017, 09, 4, 1, 0, 0, DateTimeKind.Utc); // ticket issue on 04 Sep at 1:00 AM UTC (Monday)
            var departureAt = new DateTime(2017, 09, 10, 1, 0, 0, DateTimeKind.Utc);

            var lastVoidAt = _service.CalculateLastVoidTime(_sourceRix, _carrier, departureAt, _marketingCarriers, issueAt);

            var expectedVoidAt = issueAt.AddHours(24).AddMinutes(-options.VoidSafetyTime);

            Assert.Equal(issueAt.Year, expectedVoidAt.Year);
            Assert.Equal(issueAt.Month, expectedVoidAt.Month);
            Assert.Equal(issueAt.Day + 1, expectedVoidAt.Day);
            Assert.Equal(19, lastVoidAt.Hour);
            Assert.Equal(59, lastVoidAt.Minute);
        }

        [Fact]
        public void CalcLastVoidTime_Returns_3H_BeforeDeparture()
        {
            var issueAt = new DateTime(2017, 09, 1, 1, 0, 0, DateTimeKind.Utc); // ticket issue on 01 Sep at 1:00 AM UTC
            var departureAt = new DateTime(2017, 09, 1, 3, 0, 0, DateTimeKind.Utc);

            var dt = _service.CalculateLastVoidTime(_sourceRix, _carrier, departureAt, _marketingCarriers, issueAt);

            var voidAt = departureAt.AddHours(-3);
            Assert.Equal(voidAt, dt);
        }

        [Theory]
        [InlineData(17, 10, 0)]
        [InlineData(16, 0, 0)]
        [InlineData(18, 23, 58)]
        [InlineData(19, 21, 59)]
        public void CalcLastVoidTime_NextWorkingDay(int issuedDay, int issuedHour, int issuedMinute)
        {
            var issueAt = new DateTime(2019, 12, issuedDay, issuedHour, issuedMinute, 0, DateTimeKind.Utc); // ticket issued on 17 Dec at HH:MM AM UTC
            var departureAt = new DateTime(2019, 12, 22, 0, 0, 0, DateTimeKind.Utc);

            var dt = _service.CalculateLastVoidTime(_sourceRix, _carrier, departureAt, _marketingCarriers, issueAt);

            var zt = new ZonedDateTime(issueAt.ToInstant(), PCC_TZ);

            var voidAt = new DateTime(2019, 12, zt.Date.Day, 21, 59, 0, DateTimeKind.Utc)
                .AddDays(1)
                .AddMinutes(-options.VoidSafetyTime);
            Assert.Equal(voidAt, dt);
        }

        [Theory]
        [InlineData(13, 10, 0, 3)]     // Fri
        [InlineData(14, 21, 59, 2)]    // Sat
        [InlineData(15, 21, 59, 1)]    // Sun
        [InlineData(14, 23, 59, 1)]    // Fri ??
        [InlineData(15, 23, 59, 1)]    // Sun
        public void CalcLastVoidTime_NextMonday(int issuedDay, int issuedHour, int issuedMinute, int daysUntilMonday)
        {
            var issueAt = new DateTime(2019, 12, issuedDay, issuedHour, issuedMinute, 0, DateTimeKind.Utc); // ticket issued on XX Dec at HH:MM AM UTC
            var departureAt = new DateTime(2019, 12, 22, 0, 0, 0, DateTimeKind.Utc);

            var dt = _service.CalculateLastVoidTime(_sourceRix, _carrier, departureAt, _marketingCarriers, issueAt);

            var zt = new ZonedDateTime(issueAt.ToInstant(), PCC_TZ);

            var voidAt = new DateTime(2019, 12, zt.Date.Day + daysUntilMonday, 21, 59, 0, DateTimeKind.Utc)
                .AddMinutes(-options.VoidSafetyTime);
            Assert.Equal(voidAt, dt);
        }

        [Theory]
        [InlineData(4)]
        [InlineData(14)]
        [InlineData(0)]
        public void CalcLastVoidTime_In_US_PCC_Should_Void_At_SameDay(int hourInDay)
        {
            var issueAt = new DateTime(2020, 01, 30, hourInDay, 0, 0, DateTimeKind.Utc); // Issue date at PCC
            var departureAt = new DateTime(2020, 03, 25, 1, 0, 0, DateTimeKind.Utc);

            var lastVoidAtUniversal = _service.CalculateLastVoidTime(_sourceUS, _carrier, departureAt, _marketingCarriers, issueAt);

            var lastVoidAtPCC = new ZonedDateTime(lastVoidAtUniversal.ToInstant(), US_PCC_TZ);

            var issueAtUsPcc = new ZonedDateTime(issueAt.ToInstant(), US_PCC_TZ);

            Assert.Equal(issueAtUsPcc.Day, lastVoidAtPCC.Day);
        }

        [Fact]
        public void CalcLastVoidTime_In_US_PCC_Should_Void_At_NextWorking_Day()
        {
            VoidOptions optionsUsNextWorkingDay = new VoidOptions()
            {
                VoidSafetyTime = 60,
                CustomVoidRules = new Dictionary<string, VoidRule>()
                {
                    { "1A.FLL1S21DX", VoidRule.NextWorkingDayMidnight }
                }
            };
            
            var voidService = CreateService(optionsUsNextWorkingDay, DateTime.UtcNow);

            var issueAt = new DateTime(2020, 05, 08, 10, 0, 0, DateTimeKind.Utc); // Issue date at PCC
            var departureAt = new DateTime(2020, 05, 11, 15, 0, 0, DateTimeKind.Utc);

            var lastVoidAtUniversal = voidService.CalculateLastVoidTime(_sourceUS, _carrier, departureAt, _marketingCarriers, issueAt);

            var lastVoidAtPCC = new ZonedDateTime(lastVoidAtUniversal.ToInstant(), US_PCC_TZ);

            Assert.Equal(issueAt.Day + 3, lastVoidAtPCC.Day);
        }

        [Fact]
        public void CalcLastVoidTime_In_US_PCC_Should_Void_At_NextWorkingDayMidnight_BUG_CT4279()
        {
            VoidOptions optionsUsNextWorkingDay = new VoidOptions()
            {
                VoidSafetyTime = 60,
                CustomVoidRules = new Dictionary<string, VoidRule>()
                {
                    { "1A.FLL1S21DX", VoidRule.NextWorkingDayMidnight }
                }
            };
            var date = new DateTime(2021, 03, 31, 4, 57, 0);
            var voidService = CreateService(optionsUsNextWorkingDay, date);

            var issueAt = new DateTime(2021, 03, 30, 2, 57, 0, DateTimeKind.Utc); //2021-03-29 22:57 by NY time
            var departureAt = new DateTime(2021, 04, 1, 12, 35, 0, DateTimeKind.Utc);

            var expectedLastVoidAtUniversal = new DateTime(2021, 03, 31, 2, 59, 0, DateTimeKind.Utc);
            var lastVoidAtUniversal = voidService.CalculateLastVoidTime(_sourceUS, _carrier, departureAt, _marketingCarriers, issueAt);

            var lastVoidAtPCC = new ZonedDateTime(lastVoidAtUniversal.ToInstant(), US_PCC_TZ);
            var expectedlastVoidAtPCC = new ZonedDateTime(expectedLastVoidAtUniversal.ToInstant(), US_PCC_TZ);

            Assert.Equal(lastVoidAtUniversal, expectedLastVoidAtUniversal);
            Assert.Equal(lastVoidAtPCC, expectedlastVoidAtPCC);
        }

        [Fact]
        public void CalcLastVoidTime_In_US_PCC_Should_Void_At_NextWorkingDayMidnight_SameDay_BUG_CT4279()
        {
            VoidOptions optionsUsNextWorkingDay = new VoidOptions()
            {
                VoidSafetyTime = 60,
                CustomVoidRules = new Dictionary<string, VoidRule>()
                {
                    { "1A.FLL1S21DX", VoidRule.NextWorkingDayMidnight }
                }
            };
            var date = new DateTime(2021, 03, 31, 4, 57, 0);
            var voidService = CreateService(optionsUsNextWorkingDay, date);

            var issueAt = new DateTime(2021, 03, 30, 5, 57, 0, DateTimeKind.Utc);
            var departureAt = new DateTime(2021, 04, 1, 12, 35, 0, DateTimeKind.Utc);

            var expectedLastVoidAtUniversal = new DateTime(2021, 04, 01, 2, 59, 0, DateTimeKind.Utc);
            var lastVoidAtUniversal = voidService.CalculateLastVoidTime(_sourceUS, _carrier, departureAt, _marketingCarriers, issueAt);

            var lastVoidAtPCC = new ZonedDateTime(lastVoidAtUniversal.ToInstant(), US_PCC_TZ);
            var expectedlastVoidAtPCC = new ZonedDateTime(expectedLastVoidAtUniversal.ToInstant(), US_PCC_TZ);

            Assert.Equal(lastVoidAtUniversal, expectedLastVoidAtUniversal);
            Assert.Equal(lastVoidAtPCC, expectedlastVoidAtPCC);
        }

        [Fact]
        public void CalcLastVoidTime_Returns_24H_BeforeDeparture_SpecialCarrierRule_7W_RIX()
        {
            var issueAt = new DateTime(2020, 11, 1, 0, 0, 0, DateTimeKind.Utc);     // ticket issue at 01 Nov at 00:00 AM UTC
            var departureAt = new DateTime(2020, 11, 2, 1, 0, 0, DateTimeKind.Utc);     // departure at    02 Nov at 01:00 AM UTC

            var dt = _service.CalculateLastVoidTime(_sourceRix, "7W", departureAt, _marketingCarriers, issueAt);

            var voidAt = departureAt.AddHours(-24).AddMinutes(-options.VoidSafetyTime);
            Assert.Equal(voidAt, dt);
        }

        [Fact]
        public void CalcLastVoidTime_Returns_24H_BeforeDeparture_SpecialCarrierRule_7W_US()
        {
            var issueAt = new DateTime(2021, 01, 28, 12, 21, 0, DateTimeKind.Utc);      // ticket issue at 28 Jan at 12:21 AM UTC
            var departureAt = new DateTime(2021, 01, 29, 21, 0, 0, DateTimeKind.Utc);   // departure at    29 Jan at 20:00 AM UTC

            var dt = _service.CalculateLastVoidTime(_sourceUS, "7W", departureAt, _marketingCarriers, issueAt);

            var voidAt = departureAt.AddHours(-24).AddMinutes(-options.VoidSafetyTime);    // 24h before departure + 1h safety time
            Assert.Equal(voidAt, dt);
        }

        [Fact]
        public void CalcLastVoidTime_Returns_24H_BeforeDeparture_SpecialCarrierRule_7W_AT()
        {
            var issueAt = new DateTime(2020, 12, 18, 12, 34, 04, DateTimeKind.Utc);  // ticket issued at    18 Dec 12:34 AM UTC
            var departureAt = new DateTime(2020, 12, 18, 14, 10, 00, DateTimeKind.Utc);  // departure at        18 Dec 14:10 AM UTC

            var dt = _service.CalculateLastVoidTime(_sourceLS, "7W", departureAt, _marketingCarriers, issueAt);

            var voidAt = departureAt.AddHours(-24).AddMinutes(-options.VoidSafetyTime);

            Assert.Equal(voidAt, dt);
        }

        [Fact]
        public void CalcLastVoidTime_Returns_Midnight_IssuedDate_SpecialCarrierRule_LS()
        {
            var issueAt = new DateTime(2021, 01, 28, 12, 21, 0, DateTimeKind.Utc);      // ticket issue at 28 Jan at 12:21 AM UTC
            var departureAt = new DateTime(2021, 01, 30, 21, 0, 0, DateTimeKind.Utc);   // departure at    30 Jan at 21:00 AM UTC

            var dt = _service.CalculateLastVoidTime(_sourceLS, "7W", departureAt, _marketingCarriers, issueAt);

            var voidAt = new DateTime(2021, 01, 28, 23, 0, 0, DateTimeKind.Utc);  // Midnight of the issue date (- safetyTime) at PCC Timezone (GMT +0)
            Assert.Equal(voidAt, dt);
        }

        [Fact]
        public void CalcLastVoidTime_Returns_Midnight_IssuedDate_SpecialCarrierRule_AT()
        {
            var issueAt = new DateTime(2021, 01, 28, 12, 21, 0, DateTimeKind.Utc);      // ticket issue at 28 Jan at 12:21 AM UTC
            var departureAt = new DateTime(2021, 01, 30, 21, 0, 0, DateTimeKind.Utc);   // departure at    30 Jan at 21:00 AM UTC

            var dt = _service.CalculateLastVoidTime(_sourceAT, "7W", departureAt, _marketingCarriers, issueAt);

            var voidAt = new DateTime(2021, 01, 28, 21, 0, 0, DateTimeKind.Utc);  // Midnight of the issue date (- safetyTime) at PCC Timezone (GMT +2)
            Assert.Equal(voidAt, dt);
        }

        [Fact]
        public void CalcLastVoidTime_Returns_Midnight_IssuedDate_SpecialCarrierRule_US()
        {
            var issueAt = new DateTime(2021, 01, 28, 12, 21, 0, DateTimeKind.Utc);      // ticket issue at 28 Jan at 12:21 AM UTC
            var departureAt = new DateTime(2021, 01, 30, 21, 0, 0, DateTimeKind.Utc);   // departure at    30 Jan at 21:00 AM UTC

            var dt = _service.CalculateLastVoidTime(_sourceUS, "7W", departureAt, _marketingCarriers, issueAt);

            // Midnight of the issue date (- safetyTime) at PCC Timezone (GMT -5) 
            var voidAt = new DateTime(2021, 01, 29, 4, 0, 0, DateTimeKind.Utc); // 29-01-2021 04:00 (US time) is equal to 28-01-2021 23:00 (GMT time)
            Assert.Equal(voidAt, dt);
        }
    }
}
