using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

using Xunit;
using Moq;
using FluentAssertions;

using AutoMapper;
using CTeleport.Common.Helpers;
using CTeleport.Common.Authorization.Services;
using CTeleport.Services.Booking.Clients;
using CTeleport.Services.Booking.Services;
using CTeleport.Services.Booking.Repositories;
using CTeleport.Services.Booking.Models;
using CTeleport.Services.Booking.Shared.Models;
using CTeleport.Services.Booking.Configuration;
using Microsoft.AspNetCore.Http;
using System.Security.Claims;
using CTeleport.Services.Airlines;
using CTeleport.Services.Airlines.Models;
using CTeleport.Services.Booking.Api;
using CTeleport.Services.Booking.Services.Export;
using CTeleport.Services.Helpers;
using CTeleport.Services.PlacesApiClient;
using CTeleport.Services.Providers.Client;
using CTeleport.Services.Search.Shared.Models;
using CTeleport.Services.Settings.Clients;
using CTeleport.Services.Settings.Contracts.TenantManagementService;
using CTeleport.Services.TenantShared.Models;
using Serilog.Core;
using IAuthService = CTeleport.Services.Booking.Services.IAuthService;
using CTeleport.Services.CustomFields.Services.Interfaces.Legacy;
using LegacyModels = CTeleport.Services.CustomFields.Models.Legacy;
using LegacyPublicContract = CTeleport.Services.CustomFields.Dto.LegacyPublicContract;
using Metadata = CTeleport.Services.Booking.Models.Metadata;

namespace CTeleport.Services.Booking.Tests
{
    public class BookingExportServiceTests
    {
        private readonly IMapper _mapper;
        private readonly BookingExportService _service;
        private readonly IServiceContext _context;

        static string USER_ID = "USER01";

        static string BOOKING_A_ID = Id.New();
        static string BOOKING_A_TICKET_NUMBER = "0805267490773";
        static string TENANT_A_ID = Id.New();
        static string BOOKING_A_PAX_LASTNAME = "LastnameA";

        static Models.Booking BOOKING_A = new()
        {
            Id = BOOKING_A_ID,
            TenantId = TENANT_A_ID,
            Passenger = new PassengerDetails
            {
                LastName = BOOKING_A_PAX_LASTNAME
            },
            Metadata = new Metadata(),
            Legs = new List<Leg>()
        };

        static string BOOKING_B_ID = Id.New();
        static string BOOKING_B_TICKET_NUMBER = "1175267490774";
        static string TENANT_B_ID = Id.New();
        static string BOOKING_B_PAX_LASTNAME = "LastnameB";

        static Models.Booking BOOKING_B = new()
        {
            Id = BOOKING_B_ID,
            TenantId = TENANT_B_ID,
            Passenger = new PassengerDetails
            {
                LastName = BOOKING_B_PAX_LASTNAME
            },
            Legs = new List<Leg>()
        };

        static string RESERVATION_A_LOCATOR = "RLA";

        private static Reservation RESERVATION_A = new()
        {
            Id = Id.New(),
            BookingId = BOOKING_A_ID,
            CreatedAt = DateTime.UtcNow,
            Passenger = new ReservationPassenger
            {
                LastName = BOOKING_A_PAX_LASTNAME
            },
            Locators = new Dictionary<string, string>
            {
                { LocatorNames.PROVIDER, RESERVATION_A_LOCATOR }
            },
            LegSegments = new[]
            {
                new[]
                {
                    new Segment
                    {
                        CabinClass = "Y"
                    }
                }
            }
        };

        static Reservation RESERVATION_B = new()
        {
            Id = Id.New(),
            BookingId = BOOKING_B_ID,
            Passenger = new ReservationPassenger
            {
                LastName = BOOKING_B_PAX_LASTNAME
            },
            Tickets = new List<Ticket>
            {
                new()
                {
                    Number = "11111111"
                }
            },
            LegSegments = new[]
            {
                new[]
                {
                    new Segment
                    {
                        CabinClass = "Y"
                    }
                }
            }
        };

        private static readonly BookingExportOptions options = new()
        {
            TenantMapping = new Dictionary<string, TenantExportItem>
            {
                { "gmt-*", new TenantExportItem
                    {
                        PriceRoundToCeiling = true
                    }
                }
            },
            Default = new TenantExportItem
            {
                PriceRoundToCeiling = false
            }
        };

        private readonly Mock<IBookingsRepository> _bookingsRepositoryMock;
        private readonly Mock<IReservationsRepository> _reservationsRepositoryMock;
        private readonly Mock<IBillingClient> _billingClient;
        private readonly Mock<ICustomFieldInfoService> _customFieldsInfoService;
        private readonly Mock<IHttpContextAccessor> _httpContextAccessorMock;
        private readonly Mock<ITenantManagementServiceClient> _tenantManagementServiceMock;
        private readonly Mock<IPlacesClient> _placesClientMock;
        private readonly Mock<IAirlinesClient> _airlinesClientMock;
        private readonly Mock<ICO2EmissionsService> _co2EmissionsServiceMock;

        public BookingExportServiceTests()
        {
            _bookingsRepositoryMock = new Mock<IBookingsRepository>();

            _bookingsRepositoryMock
                .Setup(r => r.GetAsync(BOOKING_A_ID))
                .ReturnsAsync(() => BOOKING_A);

            _bookingsRepositoryMock
                .Setup(r => r.GetAsync(BOOKING_B_ID))
                .ReturnsAsync(() => BOOKING_B);

            _reservationsRepositoryMock = new Mock<IReservationsRepository>();

            _reservationsRepositoryMock
                .Setup(r => r.GetByTicketNumberAsync(BOOKING_B_TICKET_NUMBER))
                .ReturnsAsync(() => RESERVATION_B);

            _billingClient = new Mock<IBillingClient>();
            _billingClient.Setup(c => c.GetInvoicesByNumbersAsync(It.IsAny<List<string>>()))
                .ReturnsAsync(new List<InvoiceDto>());
            _httpContextAccessorMock = new Mock<IHttpContextAccessor>();
            _httpContextAccessorMock.Setup(r => r.HttpContext.User.HasClaim(It.IsAny<Predicate<Claim>>()))
                .Returns(true);
            _customFieldsInfoService = new Mock<ICustomFieldInfoService>();

            _tenantManagementServiceMock = new Mock<ITenantManagementServiceClient>();
            _tenantManagementServiceMock.Setup(t => t.GetTenantAsync(It.IsAny<string>(), It.IsAny<bool>())).ReturnsAsync(new Tenant
            {
                TenantFeatures = new TenantFeatures()
            });
            
            _placesClientMock = new Mock<IPlacesClient>();
            _placesClientMock.Setup(t => t.GetAirportsByCodeAsync(It.IsAny<string[]>())).ReturnsAsync(Array.Empty<Airport>());
            
            _airlinesClientMock = new Mock<IAirlinesClient>();
            _airlinesClientMock.Setup(t => t.GetAirlinesByCodeAsync(It.IsAny<string[]>())).ReturnsAsync(Array.Empty<Airline>());

            _co2EmissionsServiceMock = new Mock<ICO2EmissionsService>();
            _co2EmissionsServiceMock.Setup(t => t.GetCO2Emissions(It.IsAny<Reservation[]>())).ReturnsAsync(new Dictionary<string, double>());

            _mapper = AutoMapperConfig.InitializeMapper();
            _context = ServiceContext.UserContext(TENANT_A_ID, new Common.Authorization.Models.User { Id = USER_ID });
            _service = new BookingExportService(_context, _httpContextAccessorMock.Object, Logger.None, 
                _mapper,
                _bookingsRepositoryMock.Object, _reservationsRepositoryMock.Object, _billingClient.Object,
                new Mock<IAuthService>().Object, options, _customFieldsInfoService.Object,
                _tenantManagementServiceMock.Object, _placesClientMock.Object, _airlinesClientMock.Object, new Mock<IProvidersClient>().Object, _co2EmissionsServiceMock.Object);
        }

        [Fact]
        public async Task GetBookingsForExportAsync_should_not_thow_exception_if_reservations_has_no_tickets()
        {
            _bookingsRepositoryMock.Setup(r => r.GetAllScopedAsync(new List<List<FilterCondition>> { }, It.IsAny<DateTime?>(), null))
                .ReturnsAsync(new List<Models.Booking>
                {
                    BOOKING_A,
                    BOOKING_B
                });

            _reservationsRepositoryMock.Setup(r => r.GetAllAsync(new List<string> { BOOKING_A.Id, BOOKING_B.Id }))
                .ReturnsAsync(new List<Reservation>
                {
                    RESERVATION_A,
                    RESERVATION_B
                });
            
            var (export, skipColumns) = await _service.GetBookingsForExportAsync(null);

            export.Should().HaveCount(2);
        }

        [Fact]
        public async Task GetBookingsForExportAsync_should_round_price_to_ceiling_for_tenant_GMT()
        {
            IServiceContext context = ServiceContext.UserContext("gmt-test", new Common.Authorization.Models.User { Id = USER_ID });
            var service = new BookingExportService(context, _httpContextAccessorMock.Object, Logger.None, _mapper,
                 _bookingsRepositoryMock.Object, _reservationsRepositoryMock.Object, _billingClient.Object,
                 Mock.Of<IAuthService>(), options, _customFieldsInfoService.Object,
                 _tenantManagementServiceMock.Object, _placesClientMock.Object, _airlinesClientMock.Object, Mock.Of<IProvidersClient>(), _co2EmissionsServiceMock.Object);

            var bookings = new List<Models.Booking>
            {
                BOOKING_A
            };

            bookings[0].Price = new BookingPrice { Total = 99.75m };

            var reservations = new List<Reservation>
            {
                RESERVATION_A
            };

            _bookingsRepositoryMock
                .Setup(r => r.GetAllScopedAsync(It.IsAny<IEnumerable<IEnumerable<FilterCondition>>>(), It.IsAny<DateTime?>(), It.IsAny<DateTime?>()))
                .ReturnsAsync(bookings);

            _reservationsRepositoryMock
                .Setup(r => r.GetAllAsync(It.IsAny<IEnumerable<string>>()))
                .ReturnsAsync(reservations);
            
            var (export, skipColumns) = await service.GetBookingsForExportAsync(null);

            Assert.Equal(100m, export.First().PriceTotal);
        }

        [Fact]
        public async Task GetBookingsForExportAsync_should_get_custom_fields()
        {
            IServiceContext context = ServiceContext.UserContext("gmt-customfields-test", new Common.Authorization.Models.User { Id = USER_ID });

            var service = new BookingExportService(context, _httpContextAccessorMock.Object, Logger.None, _mapper,
              _bookingsRepositoryMock.Object, _reservationsRepositoryMock.Object, _billingClient.Object,
              Mock.Of<IAuthService>(), options, _customFieldsInfoService.Object,
              _tenantManagementServiceMock.Object, _placesClientMock.Object, _airlinesClientMock.Object, Mock.Of<IProvidersClient>(), _co2EmissionsServiceMock.Object);


            var bookings = new List<Models.Booking>
            {
                BOOKING_A
            };

            var reservations = new List<Reservation>
            {
                new()
                {
                    Id = Id.New(),
                    BookingId = BOOKING_A_ID,
                    CreatedAt = DateTime.UtcNow,
                    Passenger = new ReservationPassenger
                    {
                        LastName = BOOKING_A_PAX_LASTNAME
                    },
                    Locators = new Dictionary<string, string>
                    {
                        { LocatorNames.PROVIDER, RESERVATION_A_LOCATOR }
                    },
                    LegSegments = new[]
                    {
                        new[]
                        {
                            new Segment
                            {
                                CabinClass = "Y"
                            }
                        }
                    }
                }
            };

            bookings.First().Metadata.CustomFields = new Dictionary<string, string>
            {
                { "CustomFieldTitle", "CFValue1" }
            };

            _customFieldsInfoService
                .Setup(r => r.GetTenantCustomFieldsAsync(It.IsAny<string>(),
                    It.IsAny<Func<LegacyModels.CustomField, bool>>(),
                    It.IsAny<Func<LegacyModels.CustomOption, bool>>()))
                .ReturnsAsync(new LegacyPublicContract.TenantCustomFieldsInfoDto
                {
                    CustomFields = new List<LegacyPublicContract.CustomFieldMetadataDto>
                    {
                        new()
                        {
                            FieldName = "CustomFieldTitle",
                            FieldType = "String",
                            Name = "Custom Field Name"
                        }
                    }
                });

            _bookingsRepositoryMock
                .Setup(r => r.GetAllScopedAsync(It.IsAny<IEnumerable<IEnumerable<FilterCondition>>>(), It.IsAny<DateTime?>(), It.IsAny<DateTime?>()))
                .ReturnsAsync(bookings);

            _reservationsRepositoryMock
                .Setup(r => r.GetAllAsync(It.IsAny<IEnumerable<string>>()))
                .ReturnsAsync(reservations);

            var (export, skipColumns) = await service.GetBookingsForExportAsync(null);

            export.Should().HaveCount(1);
            export.First().CustomFields.Should().HaveCount(1);
            export.First().CustomFields.Should().ContainKey("Custom Field Name");
            export.First().CustomFields.Should().ContainValue("CFValue1");
        }
    }
}
