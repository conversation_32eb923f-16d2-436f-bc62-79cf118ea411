using CTeleport.Common.Exceptions.Interfaces;
using CTeleport.Common.Messaging.Services;
using CTeleport.Common.Redis;
using CTeleport.Messages.Commands.Reservations;
using CTeleport.Messages.Events.Reservations;
using CTeleport.Services.Booking.Core.Dto;
using CTeleport.Services.Booking.Core.Interfaces;
using CTeleport.Services.Booking.Handlers;
using CTeleport.Services.Booking.Models;
using CTeleport.Services.Booking.Services;
using CTeleport.Services.Booking.Services.Interfaces;
using CTeleport.Services.Booking.Shared.Enums;
using Moq;
using Serilog;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Xunit;

namespace CTeleport.Services.Booking.Tests
{
    public class CheckPendingReservationsHandlerTests
    {
        private readonly MockRepository _mockRepository;

        private readonly Mock<IProviderReservationInfoService> _providerReservationServiceMock;
        private readonly Mock<IPendingReservationService> _pendingReservationServiceMock;
        private readonly Mock<IBookingMetricsService> _metricsServiceMock;
        private readonly Mock<IMessageDispatcher> _dispatcherMock;
        private readonly Mock<ILogger> _loggerMock;

        private readonly IHandlerFactory _handlerFactory;
        private readonly ICommandHandler<CheckPendingReservations> _handler;

        private PendingReservation PENDING_RESERVATION1 = new PendingReservation
        {
            Locators = new Dictionary<string, string>
            {
                { "Provider", "C1E98P" }
            },
            State = ProviderReservationState.Pending,
            ExpireAt = DateTime.UtcNow.AddHours(2)
        };

        private static PendingReservation PENDING_RESERVATION2 = new PendingReservation
        {
            Locators = new Dictionary<string, string>
            {
                { "Provider", "F8AS09" }
            },
            State = ProviderReservationState.Pending
        };

        private static PendingReservation PENDING_RESERVATION3 = new PendingReservation
        {
            Locators = new Dictionary<string, string>
            {
                { "Provider", "P12E98F" }
            },
            State = ProviderReservationState.Pending
        };

        private static PendingReservation PENDING_RESERVATION4 = new PendingReservation
        {
            Locators = new Dictionary<string, string>
            {
                { "Provider", "OR98TY" }
            },
            State = ProviderReservationState.Pending
        };

        public CheckPendingReservationsHandlerTests()
        {
            _mockRepository = new MockRepository(MockBehavior.Loose);

            _providerReservationServiceMock = _mockRepository.Create<IProviderReservationInfoService>();
            _pendingReservationServiceMock = _mockRepository.Create<IPendingReservationService>();
            _dispatcherMock = _mockRepository.Create<IMessageDispatcher>();
            _metricsServiceMock = _mockRepository.Create<IBookingMetricsService>();
            _loggerMock = _mockRepository.Create<ILogger>();
            
            _handlerFactory = new HandlerFactory(_mockRepository.Create<IExceptionHandler>().Object, _mockRepository.Create<ILockFactory>().Object);

            _handler = new CheckPendingReservationsHandler(
                _providerReservationServiceMock.Object,
                _pendingReservationServiceMock.Object,
                _metricsServiceMock.Object,
                _handlerFactory,
                _dispatcherMock.Object,
                _loggerMock.Object);

            _pendingReservationServiceMock
                .Setup(r => r.GetActivePendingReservationsAsync(10))
                .ReturnsAsync(new []
                {
                    PENDING_RESERVATION1,
                    PENDING_RESERVATION2,
                    PENDING_RESERVATION3,
                    PENDING_RESERVATION4
                });

            _providerReservationServiceMock
                .Setup(r => r.GetReservationAsync(It.Is<ProviderRetrieveReservationRequest>(e => e.Locators["Provider"] == "C1E98P") ) )
                .ReturnsAsync(new ProviderRetrieveReservationResponse
                {
                    State = ProviderReservationState.Pending
                });

            _providerReservationServiceMock
                .Setup(r => r.GetReservationAsync(It.Is<ProviderRetrieveReservationRequest>(e => e.Locators["Provider"] == "F8AS09") ) )
                .ReturnsAsync(new ProviderRetrieveReservationResponse
                {
                    State = ProviderReservationState.Confirmed
                });

            _providerReservationServiceMock
                .Setup(r => r.GetReservationAsync(It.Is<ProviderRetrieveReservationRequest>(e => e.Locators["Provider"] == "P12E98F") ) )
                .ReturnsAsync(new ProviderRetrieveReservationResponse
                {
                    State = ProviderReservationState.Confirmed
                });

            _providerReservationServiceMock
                .Setup(r => r.GetReservationAsync(It.Is<ProviderRetrieveReservationRequest>(e => e.Locators["Provider"] == "OR98TY") ) )
                .ReturnsAsync(new ProviderRetrieveReservationResponse
                {
                    State = ProviderReservationState.Failed
                });
        }

        [Fact]
        public async Task ShouldHandleConfirmedReservationsAsync()
        {
            // Act
            await _handler.HandleAsync(new CheckPendingReservations());

            // Assert
            _dispatcherMock
                .Verify(x => x.DispatchAsync(It.IsAny<ReservationPendingCompleted>()),
                    Times.Exactly(2));

            _pendingReservationServiceMock
                .Verify(x => x.UpdatePendingReservationAsync(It.Is<PendingReservation>(e => e.State == ProviderReservationState.Confirmed)),
                    Times.Exactly(2));
        }

        [Fact]
        public async Task ShouldHandleFailedReservationsAsync()
        {
            // Act
            await _handler.HandleAsync(new CheckPendingReservations());

            // Assert
            _dispatcherMock
                .Verify(x => x.DispatchAsync(It.IsAny<ReservationPendingRejected>()),
                    Times.Exactly(1));

            _pendingReservationServiceMock
                .Verify(x => x.UpdatePendingReservationAsync(It.Is<PendingReservation>(e => e.State == ProviderReservationState.Failed)),
                    Times.Exactly(1));
        }

        [Fact]
        public async Task ShouldHandlePendingReservationsAsync()
        {
            // Arrange
            PENDING_RESERVATION1.ExpireAt = DateTime.UtcNow.AddHours(2);

            // Act
            await _handler.HandleAsync(new CheckPendingReservations());

            // Assert
            _pendingReservationServiceMock
                .Verify(x => x.UpdatePendingReservationAsync(It.Is<PendingReservation>(e => e.State == ProviderReservationState.Pending)),
                    Times.Exactly(1));
        }

        [Fact]
        public async Task ShouldHandleExpiredReservationsAsync()
        {
            // Arrange
            PENDING_RESERVATION1.ExpireAt = DateTime.UtcNow.AddHours(-2);

            // Act
            await _handler.HandleAsync(new CheckPendingReservations());

            // Assert
            _dispatcherMock
                .Verify(x => x.DispatchAsync(It.IsAny<ReservationPendingRejected>()),
                    Times.Exactly(2));

            _pendingReservationServiceMock
                .Verify(x => x.UpdatePendingReservationAsync(It.Is<PendingReservation>(e => e.State == ProviderReservationState.Pending)),
                    Times.Exactly(0));
        }
    }
}