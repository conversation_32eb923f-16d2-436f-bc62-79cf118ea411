using CTeleport.Services.Booking.Configuration;
using CTeleport.Services.Booking.Extensions;
using CTeleport.Services.Booking.Models;
using CTeleport.Services.Booking.Tests.TicketStateTestData;
using FluentAssertions;
using Xunit;

namespace CTeleport.Services.Booking.Tests
{
    public class TicketStateValidationOptionsExtensionsTests
    {
        [Theory]
        [ClassData(typeof(CooldownTestData))]
        public void Cooldown(string name, TicketStateValidationOptions options, Reservation reservation, bool expectedIsDuringCooldown, bool expectedIsAfterCoolDown)
        {
            var result = options.For(reservation);
            result.IsDuringCooldownPeriod().Should().Be(expectedIsDuringCooldown, name);
            result.IsAfterCoolDownPeriod().Should().Be(expectedIsAfterCoolDown, name);
            result.CooldownHoursAfterDeparture.Should().Be(options.CooldownEndHoursAfterDeparture);

            var resultByDate = options.For(reservation.DepartureAt);
            resultByDate.Should().BeEquivalentTo(result);
        }
    }
}
