using System.Linq;
using CTeleport.Services.Booking.Clients;
using CTeleport.Services.Booking.Clients.Authz;
using FluentAssertions;
using Newtonsoft.Json;
using Xunit;

namespace CTeleport.Services.Booking.Tests
{
    public class PartialResultTermsConverterTests
    {
        [Fact]
        public void Converter_shouldParseTermsFromJSON()
        {
            var json =
                "{\"result\":{\"queries\":[[{\"index\":0,\"terms\":[{\"type\":\"ref\",\"value\":[{\"type\":\"var\",\"value\":\"neq\"}]},{\"type\":\"string\",\"value\":\"cteleport\"},{\"type\":\"ref\",\"value\":[{\"type\":\"var\",\"value\":\"input\"},{\"type\":\"string\",\"value\":\"resource\"},{\"type\":\"string\",\"value\":\"tenant_id\"}]}]},{\"index\":1,\"terms\":[{\"type\":\"ref\",\"value\":[{\"type\":\"var\",\"value\":\"eq\"}]},{\"type\":\"string\",\"value\":\"auth0|abcd\"},{\"type\":\"ref\",\"value\":[{\"type\":\"var\",\"value\":\"input\"},{\"type\":\"string\",\"value\":\"resource\"},{\"type\":\"string\",\"value\":\"created_by\"},{\"type\":\"string\",\"value\":\"id\"}]}]}]]}}"; 
            var result = JsonConvert.DeserializeObject<AuthzClient.OpenPolicyAgentCompileResponse>(json, new PartialResultTermsConverter());
            result.Result.Queries.Should().NotBeNull();
            result.Result.Queries.SelectMany(x => x).Where(x => x.Terms.Any(term => term is RefTerm)).Should()
                .HaveCount(2);
            result.Result.Queries.First().Where(x => x.Operator() == "eq").Should().HaveCount(1);
            result.Result.Queries.FirstOrDefault()?.Where(x => x.Operands().Any(o => !o.IsConstant() && o.ToString() == "input.resource.tenant_id")).Should()
                .HaveCount(1);
        }
    }
}