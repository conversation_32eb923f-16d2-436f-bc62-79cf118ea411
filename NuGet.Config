<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <packageSources>
    <add key="nuget.org" value="https://api.nuget.org/v3/index.json" protocolVersion="3" />
    <add key="gitlab" value="https://gitlab.cteleport.com/api/v4/projects/15/packages/nuget/index.json" />
  </packageSources>
  <packageRestore>
      <add key="enabled" value="True" />
      <add key="automatic" value="False" />
  </packageRestore>
  <bindingRedirects>
    <add key="skip" value="False" />
  </bindingRedirects>
  <packageManagement>
    <add key="format" value="1" />
    <add key="disabled" value="False" />
  </packageManagement>
  <packageSourceCredentials>
    <gitlab>
      <add key="Username" value="%GITLAB_NUGET_REGISTRY_USERNAME%" />
      <add key="ClearTextPassword" value="%GITLAB_NUGET_REGISTRY_PASSWORD%" />
    </gitlab>
  </packageSourceCredentials>
</configuration>
